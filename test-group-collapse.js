/**
 * 测试分组折叠功能的修复
 */

console.log('🧪 测试分组折叠功能修复...\n');

// 模拟分组结构
const testGroupStructure = {
    // 分组1 (depth: 0)
    group1: {
        id: 'dept_0_0',
        depth: 0,
        range: [0, 9], // 包含记录 0-9
        children: {
            // 分组1.1 (depth: 1)
            group11: {
                id: 'project_1_0',
                depth: 1,
                range: [0, 4], // 包含记录 0-4
                children: {
                    // 分组1.1.1 (depth: 2)
                    group111: {
                        id: 'priority_2_0',
                        depth: 2,
                        range: [0, 2] // 包含记录 0-2
                    }
                }
            },
            // 分组1.2 (depth: 1)
            group12: {
                id: 'project_1_1',
                depth: 1,
                range: [5, 9] // 包含记录 5-9
            }
        }
    }
};

// 模拟 GroupCalculator 的关键方法
class MockGroupCalculator {
    constructor() {
        this.groupCollapseState = new Set();
        this.groupBreakpoints = new Map([
            ['dept', [0]], // 部门分组在记录0开始
            ['project', [0, 5]], // 项目分组在记录0和5开始
            ['priority', [0]] // 优先级分组在记录0开始
        ]);
    }

    // 模拟折叠分组1
    collapseGroup1() {
        this.groupCollapseState.add('dept_0_0');
        console.log('📁 折叠分组1 (dept_0_0)');
    }

    // 获取分组的记录范围
    getGroupRange(groupId) {
        const parts = groupId.split('_');
        if (parts.length >= 3) {
            const fieldId = parts[0];
            const breakpointIndex = parseInt(parts[2]);
            const breakpoints = this.groupBreakpoints.get(fieldId) || [];
            
            if (breakpointIndex < breakpoints.length) {
                const startIndex = breakpoints[breakpointIndex];
                let endIndex;
                
                if (breakpointIndex + 1 < breakpoints.length) {
                    endIndex = breakpoints[breakpointIndex + 1] - 1;
                } else {
                    // 最后一个分组，假设总共10条记录
                    endIndex = 9;
                }
                
                return [startIndex, endIndex];
            }
        }
        return null;
    }

    // 检查两个范围是否有重叠
    isRangeOverlap(start1, end1, start2, end2) {
        return start1 <= end2 && start2 <= end1;
    }

    // 检查分组是否被父级分组隐藏
    isGroupHiddenByParent(currentRow, parentDepth) {
        const currentDepth = currentRow.depth ?? 0;
        if (parentDepth >= currentDepth) {
            return false;
        }

        // 获取当前分组的记录范围
        const [currentStart, currentEnd] = currentRow.range || [0, 0];

        // 检查该深度的所有折叠分组
        for (const collapsedGroupId of this.groupCollapseState) {
            // 解析分组ID获取深度信息
            const parts = collapsedGroupId.split('_');
            if (parts.length >= 2) {
                const groupDepth = parseInt(parts[1]);
                if (groupDepth === parentDepth) {
                    // 找到对应深度的折叠分组，检查当前分组是否在其范围内
                    const groupRange = this.getGroupRange(collapsedGroupId);
                    if (groupRange && this.isRangeOverlap(currentStart, currentEnd, groupRange[0], groupRange[1])) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // 过滤可见的分组标签
    filterVisibleGroupTabs(groupTabRows) {
        const visibleRows = [];

        for (const currentRow of groupTabRows) {
            const currentDepth = currentRow.depth ?? 0;
            let show = true;

            // 检查当前分组的所有父级深度是否有被折叠的分组
            for (let parentDepth = 0; parentDepth < currentDepth; parentDepth++) {
                // 检查是否有任何该深度的分组被折叠，且当前分组属于其范围内
                if (this.isGroupHiddenByParent(currentRow, parentDepth)) {
                    show = false;
                    break;
                }
            }

            if (show) {
                visibleRows.push(currentRow);
            }
        }

        return visibleRows;
    }

    // 测试方法
    testGroupVisibility() {
        console.log('🔍 测试分组可见性...\n');

        // 模拟分组标签行
        const groupTabRows = [
            { id: 'dept_0_0', depth: 0, range: [0, 9], name: '分组1' },
            { id: 'project_1_0', depth: 1, range: [0, 4], name: '分组1.1' },
            { id: 'priority_2_0', depth: 2, range: [0, 2], name: '分组1.1.1' },
            { id: 'project_1_1', depth: 1, range: [5, 9], name: '分组1.2' }
        ];

        console.log('📋 原始分组结构:');
        groupTabRows.forEach(row => {
            console.log(`  ${row.name} (depth: ${row.depth}, range: [${row.range[0]}, ${row.range[1]}])`);
        });

        console.log('\n🔓 分组1展开时的可见分组:');
        let visibleGroups = this.filterVisibleGroupTabs(groupTabRows);
        visibleGroups.forEach(row => {
            console.log(`  ✅ ${row.name} (depth: ${row.depth})`);
        });

        console.log('\n📁 分组1折叠后的可见分组:');
        this.collapseGroup1();
        visibleGroups = this.filterVisibleGroupTabs(groupTabRows);
        visibleGroups.forEach(row => {
            console.log(`  ✅ ${row.name} (depth: ${row.depth})`);
        });

        console.log('\n🎯 验证结果:');
        const expectedVisible = ['分组1']; // 只有分组1应该可见
        const actualVisible = visibleGroups.map(row => row.name);
        
        const isCorrect = expectedVisible.length === actualVisible.length && 
                         expectedVisible.every(name => actualVisible.includes(name));
        
        if (isCorrect) {
            console.log('✅ 测试通过！分组1折叠时，所有子分组(1.1, 1.1.1, 1.2)都被正确隐藏');
        } else {
            console.log('❌ 测试失败！');
            console.log(`  预期可见: [${expectedVisible.join(', ')}]`);
            console.log(`  实际可见: [${actualVisible.join(', ')}]`);
        }

        return isCorrect;
    }
}

// 运行测试
function runTest() {
    const calculator = new MockGroupCalculator();
    const result = calculator.testGroupVisibility();
    
    console.log('\n' + '='.repeat(60));
    console.log(result ? '🎉 修复验证成功！' : '⚠️  修复需要进一步调整');
    console.log('='.repeat(60));
    
    return result;
}

// 如果直接运行此脚本
if (require.main === module) {
    try {
        runTest();
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

module.exports = { runTest, MockGroupCalculator };
