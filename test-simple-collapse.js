/**
 * 测试简化后的分组折叠功能
 */

console.log('🎯 测试简化后的分组折叠功能...\n');

// 模拟简化后的 GroupCalculator 关键方法
class SimpleGroupCalculator {
    constructor() {
        this.groupCollapseState = new Set();
        this.groups = [
            { field_id: 'dept' },
            { field_id: 'project' },
            { field_id: 'priority' }
        ];
        this.groupBreakpoints = new Map([
            ['dept', [0]], // 部门分组在记录0开始
            ['project', [0, 5]], // 项目分组在记录0和5开始
            ['priority', [0]] // 优先级分组在记录0开始
        ]);
    }

    // 模拟生成分组标签，同时标记需要隐藏的元素
    generateGroupTabRows(recordIndex, hiddenElementIds) {
        const groupTabRows = [];
        const collapsedAncestors = new Set(); // 记录被折叠的祖先深度

        this.groups.forEach((groupField, depth) => {
            const breakpoints = this.groupBreakpoints.get(groupField.field_id) || [];

            if (breakpoints.includes(recordIndex)) {
                const groupId = `${groupField.field_id}_${depth}_${breakpoints.indexOf(recordIndex)}`;
                const isCollapsed = this.groupCollapseState.has(groupId);

                const groupRow = {
                    _id: `group_${groupId}`,
                    type: 'group',
                    depth,
                    fieldId: groupField.field_id,
                    groupValue: `分组${depth + 1}`,
                    isCollapsed,
                    groupId
                };

                groupTabRows.push(groupRow);

                // 检查是否需要隐藏当前分组
                if (hiddenElementIds) {
                    // 如果有祖先被折叠，则隐藏当前分组
                    const hasCollapsedAncestor = Array.from(collapsedAncestors).some(ancestorDepth => ancestorDepth < depth);
                    if (hasCollapsedAncestor) {
                        hiddenElementIds.add(groupRow._id);
                        console.log(`    🙈 隐藏分组: ${groupRow.groupValue} (被祖先折叠)`);
                    }
                }

                // 如果当前分组被折叠，记录其深度
                if (isCollapsed) {
                    collapsedAncestors.add(depth);
                    console.log(`    📁 分组被折叠: ${groupRow.groupValue} (depth: ${depth})`);
                }
            }
        });

        return groupTabRows;
    }

    // 模拟添加记录和添加行
    addRecordsAndAddRows(linearRows, hiddenElementIds, groupIds) {
        // 添加记录
        const recordRow = {
            _id: 'record_1',
            type: 'record',
            depth: this.groups.length
        };
        linearRows.push(recordRow);

        // 检查记录是否应该被隐藏
        const shouldHideRecord = groupIds.some(groupId => this.groupCollapseState.has(groupId));
        if (shouldHideRecord) {
            hiddenElementIds.add(recordRow._id);
            console.log(`    🙈 隐藏记录: ${recordRow._id}`);
        }

        // 添加添加行
        const addRow = {
            _id: 'add_1',
            type: 'add',
            depth: this.groups.length
        };
        linearRows.push(addRow);

        // 检查添加行是否应该被隐藏
        const shouldHideAddRow = groupIds.some(groupId => this.groupCollapseState.has(groupId));
        if (shouldHideAddRow) {
            hiddenElementIds.add(addRow._id);
            console.log(`    🙈 隐藏添加行: ${addRow._id}`);
        }
    }

    // 模拟完整的计算过程
    calculateLinearRows() {
        console.log('🔄 开始计算线性行...\n');
        
        const linearRows = [];
        const hiddenElementIds = new Set(); // 用于标记需要隐藏的元素ID

        console.log('1️⃣ 生成分组标签 (recordIndex: 0):');
        const groupTabRows = this.generateGroupTabRows(0, hiddenElementIds);
        linearRows.push(...groupTabRows);

        console.log('\n2️⃣ 添加记录和添加行:');
        const groupIds = groupTabRows.map(row => row.groupId);
        this.addRecordsAndAddRows(linearRows, hiddenElementIds, groupIds);

        console.log('\n3️⃣ 过滤前的所有行:');
        linearRows.forEach((row, index) => {
            const isHidden = hiddenElementIds.has(row._id);
            console.log(`    ${index + 1}. ${row.type} (${row._id}) ${isHidden ? '❌ 将被隐藏' : '✅ 可见'}`);
        });

        console.log('\n4️⃣ 最终过滤结果:');
        const visibleRows = linearRows.filter(row => !hiddenElementIds.has(row._id));
        visibleRows.forEach((row, index) => {
            console.log(`    ${index + 1}. ${row.type} (${row._id}) ✅`);
        });

        return { linearRows, visibleRows, hiddenElementIds };
    }

    // 测试方法
    testSimpleCollapse() {
        console.log('📋 测试场景1: 所有分组展开');
        console.log('='.repeat(50));
        const result1 = this.calculateLinearRows();

        console.log('\n📁 测试场景2: 折叠分组1 (dept_0_0)');
        console.log('='.repeat(50));
        this.groupCollapseState.add('dept_0_0');
        const result2 = this.calculateLinearRows();

        console.log('\n🎯 验证结果:');
        console.log(`场景1 - 可见行数: ${result1.visibleRows.length} (预期: 5)`);
        console.log(`场景2 - 可见行数: ${result2.visibleRows.length} (预期: 1)`);

        const test1Pass = result1.visibleRows.length === 5;
        const test2Pass = result2.visibleRows.length === 1;

        if (test1Pass && test2Pass) {
            console.log('✅ 所有测试通过！');
        } else {
            console.log('❌ 测试失败！');
        }

        return test1Pass && test2Pass;
    }
}

// 运行测试
function runSimpleTest() {
    const calculator = new SimpleGroupCalculator();
    const result = calculator.testSimpleCollapse();
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 简化方案优势:');
    console.log('  🎯 逻辑清晰：在生成阶段就标记隐藏元素');
    console.log('  🚀 性能优秀：最后只需一次简单过滤');
    console.log('  🔧 易于维护：代码结构简单明了');
    console.log('  ✨ 扩展性好：容易添加新的隐藏逻辑');
    console.log('='.repeat(60));
    console.log(result ? '🎉 简化方案验证成功！' : '⚠️  需要进一步调整');
    console.log('='.repeat(60));
    
    return result;
}

// 如果直接运行此脚本
if (require.main === module) {
    try {
        runSimpleTest();
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

module.exports = { runSimpleTest, SimpleGroupCalculator };
