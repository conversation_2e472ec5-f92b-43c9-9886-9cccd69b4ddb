{"root": true, "ignorePatterns": ["src/**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json", "e2e/tsconfig.json"], "createDefaultProgram": true}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"@angular-eslint/no-output-on-prefix": 2, "@angular-eslint/use-pipe-transform-interface": 2, "@angular-eslint/component-class-suffix": 2, "@angular-eslint/directive-class-suffix": 2, "@angular-eslint/no-empty-lifecycle-method": "off", "@angular-eslint/no-host-metadata-property": "off", "@angular-eslint/no-input-rename": "off", "@angular-eslint/no-output-native": "off", "@angular-eslint/no-output-rename": "off", "@angular-eslint/contextual-lifecycle": 2, "@typescript-eslint/member-ordering": [1, {"default": {"order": "as-written", "memberTypes": ["static-field", "instance-field", "static-method", "instance-method"]}}], "@typescript-eslint/no-explicit-any": 1, "prefer-const": 1, "no-console": [1, {"allow": ["debug", "info", "time", "timeEnd", "trace", "log"]}]}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {}}]}