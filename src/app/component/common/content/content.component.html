@if (tableService.activeView()) {
    <div class="d-flex align-items-center">
        <thy-segment
            class="mb-2 mr-2"
            thyMode="inline"
            thySize="md"
            [thyActiveIndex]="dateModeActiveIndex()"
            (thySelectChange)="changeDataMode($event)"
        >
            <thy-segment-item thyValue="default" thyIcon="bars">默认</thy-segment-item>
            <thy-segment-item thyValue="big-data" thyIcon="trend">大数据量</thy-segment-item>
        </thy-segment>
        <input
            class="mx-2 mb-2"
            thyInput
            [(ngModel)]="searchKeywords"
            (blur)="tableService.setSearchKeywords(searchKeywords)"
            (thyEnter)="tableService.setSearchKeywords(searchKeywords)"
            placeholder="搜索"
            style="width: 150px"
        />
        <a thyAction class="mb-2" thyIcon="undo" href="javascript:;" (click)="undo()" [thyDisabled]="!canUndo()"
            >撤销 {{ canUndoCount() }}</a
        >
        <a thyAction class="mb-2" thyIcon="redo" href="javascript:;" (click)="redo()" [thyDisabled]="!canRedo()"
            >重做 {{ canRedoCount() }}</a
        >
        <a thyAction class="mb-2" thyIcon="trash" href="javascript:;" (click)="removeRecord()">删除行</a>
    </div>
    <div class="table-content">
        <ai-table-grid
            [aiReadonly]="tableService.readonly()"
            [aiContextMenuItems]="contextMenuItems"
            [aiMaxFields]="tableService.maxFields()"
            [aiMaxRecords]="tableService.maxRecords()"
            [aiMaxSelectOptions]="1000"
            [(aiRecords)]="tableService.records"
            [(aiFields)]="tableService.fields"
            [(aiFieldsSizeMap)]="tableService.fieldsSizeMap"
            [aiFieldConfig]="aiFieldConfig()"
            [aiKeywords]="tableService.keywords()"
            [aiPlugins]="plugins"
            [aiReferences]="references()"
            (aiAddRecord)="addRecord($event)"
            (aiAddField)="addField($event)"
            (aiMoveField)="dragMoveField($event)"
            (aiUpdateFieldValues)="updateFieldValues($event)"
            (aiSetField)="setField($event)"
            (aiSetFieldWidth)="setFieldWidth($event)"
            (aiSetFieldStatType)="setFieldStatType($event)"
            (aiMoveRecords)="dragMoveRecords($event)"
            (aiTableInitialized)="aiTableInitialized($event)"
            [aiBuildRenderDataFn]="tableService.aiBuildRenderDataFn()"
            [aiBuildGroupLinearRowsFn]="tableService.aiBuildGroupLinearRowsFn()"
            [aiGetI18nTextByKey]="getI18nTextByKey"
            [aiFrozenColumnCountFn]="calculateFrozenCount"
            (aiClick)="onClick($event)"
            (aiDbClick)="onDbClick($event)"
            (aiRowGroupCollapseClick)="onRowGroupCollapseClick($event)"
        ></ai-table-grid>
    </div>
}
