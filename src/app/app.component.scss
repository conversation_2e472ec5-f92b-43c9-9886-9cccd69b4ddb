body {
    overflow: hidden;
}
.table-container {
    display: block;
    overflow: hidden;
    height: 100%;
    padding-top: 30px;
    margin-left: 30px;
    margin-right: 30px;
    .thy-nav-list {
        flex: none;
    }
    .thy-nav-item {
        display: flex;
        align-items: center;
    }
    .thy-tab-content {
        // 155 视图 tab 的高度
        height: calc(100vh - 180px);
        .table-content {
            // 44 是操作列的高度
            height: calc(100% - 44px);
        }
    }
}
