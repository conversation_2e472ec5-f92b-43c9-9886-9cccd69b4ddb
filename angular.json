{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "packages", "projects": {"demo": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/demo", "browser": ""}, "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "./node_modules/@tethys/icons/defs", "output": "/assets/icons/defs/"}, {"glob": "**/*.svg", "input": "src/app/icons", "output": "/assets/icons/"}], "styles": ["src/styles.scss"], "scripts": []}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"options": {"port": 6100}, "builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "demo:build:production"}, "development": {"buildTarget": "demo:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}}}, "grid": {"projectType": "library", "root": "packages/grid", "sourceRoot": "packages/grid", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "packages/grid/tsconfig.lib.json", "project": "packages/grid/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/grid/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/grid/src/test.ts", "tsConfig": "packages/grid/tsconfig.spec.json", "karmaConfig": "packages/grid/karma.conf.js", "codeCoverage": true, "codeCoverageExclude": ["packages/grid/testing/**/*"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["packages/store/**/*.ts", "packages/store/**/*.html"]}}}}, "state": {"projectType": "library", "root": "packages/state", "sourceRoot": "packages/state/src", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "packages/state/tsconfig.lib.json", "project": "packages/state/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/state/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "packages/state/src/test.ts", "tsConfig": "packages/state/tsconfig.spec.json", "karmaConfig": "packages/state/karma.conf.js", "codeCoverage": true, "codeCoverageExclude": ["packages/state/testing/**/*"]}}, "lint": {"builder": "@angular-eslint/builder:lint"}}}}, "cli": {"analytics": false}}