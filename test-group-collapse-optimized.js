/**
 * 测试优化后的分组折叠功能
 */

console.log('🚀 测试优化后的分组折叠功能...\n');

// 模拟优化后的 GroupCalculator 关键方法
class OptimizedGroupCalculator {
    constructor() {
        this.groupCollapseState = new Set();
    }

    // 优化版本：利用数据有序性，单次遍历过滤可见分组
    filterVisibleGroupTabsOptimized(groupTabRows) {
        if (groupTabRows.length === 0) {
            return [];
        }

        const visibleRows = [];
        const collapsedDepths = new Set(); // 记录当前被折叠的深度层级

        // 单次遍历，利用数据有序性
        for (const currentRow of groupTabRows) {
            const currentDepth = currentRow.depth ?? 0;

            // 清理比当前深度更深的折叠状态（因为我们进入了新的分支）
            for (const depth of collapsedDepths) {
                if (depth >= currentDepth) {
                    collapsedDepths.delete(depth);
                }
            }

            // 检查当前分组是否被折叠
            const isCollapsed = this.groupCollapseState.has(currentRow.groupId);
            if (isCollapsed) {
                collapsedDepths.add(currentDepth);
            }

            // 如果没有父级被折叠，则显示当前分组
            const hasCollapsedParent = Array.from(collapsedDepths).some((depth) => depth < currentDepth);
            if (!hasCollapsedParent) {
                visibleRows.push(currentRow);
            }
        }

        return visibleRows;
    }

    // 简化版本：检查当前分组是否应该显示添加行
    shouldShowAddRow(currentGroupIds) {
        if (!currentGroupIds || currentGroupIds.length === 0) {
            return true; // 默认显示
        }

        // 检查当前组的所有分组层级以及其父级是否有被折叠的
        return !this.hasCollapsedAncestor(currentGroupIds);
    }

    // 检查分组链中是否有被折叠的祖先分组
    hasCollapsedAncestor(groupIds) {
        // 按深度排序分组ID
        const sortedGroupIds = groupIds.map((id) => ({ id, depth: parseInt(id.split('_')[1]) || 0 })).sort((a, b) => a.depth - b.depth);

        // 检查每个层级是否被折叠
        for (const { id } of sortedGroupIds) {
            if (this.groupCollapseState.has(id)) {
                return true;
            }
        }

        return false;
    }

    // 模拟折叠分组1
    collapseGroup1() {
        this.groupCollapseState.add('dept_0_0');
        console.log('📁 折叠分组1 (dept_0_0)');
    }

    // 测试方法
    testOptimizedGroupVisibility() {
        console.log('🔍 测试优化后的分组可见性...\n');

        // 模拟按顺序排列的分组标签行
        const groupTabRows = [
            { groupId: 'dept_0_0', depth: 0, name: '分组1' },
            { groupId: 'project_1_0', depth: 1, name: '分组1.1' },
            { groupId: 'priority_2_0', depth: 2, name: '分组1.1.1' },
            { groupId: 'project_1_1', depth: 1, name: '分组1.2' }
        ];

        console.log('📋 原始分组结构（按顺序）:');
        groupTabRows.forEach((row, index) => {
            console.log(`  ${index + 1}. ${row.name} (depth: ${row.depth}, id: ${row.groupId})`);
        });

        console.log('\n🔓 分组1展开时的可见分组:');
        let visibleGroups = this.filterVisibleGroupTabsOptimized(groupTabRows);
        visibleGroups.forEach((row, index) => {
            console.log(`  ✅ ${index + 1}. ${row.name} (depth: ${row.depth})`);
        });

        console.log('\n📁 分组1折叠后的可见分组:');
        this.collapseGroup1();
        visibleGroups = this.filterVisibleGroupTabsOptimized(groupTabRows);
        visibleGroups.forEach((row, index) => {
            console.log(`  ✅ ${index + 1}. ${row.name} (depth: ${row.depth})`);
        });

        console.log('\n🎯 验证结果:');
        const expectedVisible = ['分组1']; // 只有分组1应该可见
        const actualVisible = visibleGroups.map((row) => row.name);

        const isCorrect = expectedVisible.length === actualVisible.length && expectedVisible.every((name) => actualVisible.includes(name));

        if (isCorrect) {
            console.log('✅ 分组可见性测试通过！');
        } else {
            console.log('❌ 分组可见性测试失败！');
            console.log(`  预期可见: [${expectedVisible.join(', ')}]`);
            console.log(`  实际可见: [${actualVisible.join(', ')}]`);
        }

        return isCorrect;
    }

    testOptimizedAddRowVisibility() {
        console.log('\n🔍 测试优化后的添加行可见性...\n');

        // 重置状态，确保测试环境干净
        this.groupCollapseState.clear();

        // 测试场景1：所有分组都展开
        console.log('场景1：所有分组都展开');
        const groupIds1 = ['dept_0_0', 'project_1_0', 'priority_2_0'];
        const shouldShow1 = this.shouldShowAddRow(groupIds1);
        console.log(`  分组链: [${groupIds1.join(', ')}]`);
        console.log(`  应该显示添加行: ${shouldShow1 ? '✅ 是' : '❌ 否'}`);

        // 测试场景2：分组1被折叠
        console.log('\n场景2：分组1被折叠');
        this.groupCollapseState.add('dept_0_0'); // 折叠分组1
        const groupIds2 = ['dept_0_0', 'project_1_1']; // 分组1.2的添加行
        const shouldShow2 = this.shouldShowAddRow(groupIds2);
        console.log(`  分组链: [${groupIds2.join(', ')}]`);
        console.log(`  应该显示添加行: ${shouldShow2 ? '✅ 是' : '❌ 否'}`);

        const addRowTestPassed = shouldShow1 === true && shouldShow2 === false;
        if (addRowTestPassed) {
            console.log('\n✅ 添加行可见性测试通过！');
        } else {
            console.log('\n❌ 添加行可见性测试失败！');
            console.log(`  场景1预期: true, 实际: ${shouldShow1}`);
            console.log(`  场景2预期: false, 实际: ${shouldShow2}`);
        }

        return addRowTestPassed;
    }
}

// 运行测试
function runOptimizedTest() {
    const calculator = new OptimizedGroupCalculator();

    const groupVisibilityPassed = calculator.testOptimizedGroupVisibility();
    const addRowVisibilityPassed = calculator.testOptimizedAddRowVisibility();

    const allPassed = groupVisibilityPassed && addRowVisibilityPassed;

    console.log('\n' + '='.repeat(60));
    console.log('📊 优化效果总结:');
    console.log('  ✨ 单次遍历：O(n) 时间复杂度');
    console.log('  🚀 利用数据有序性：无需嵌套循环');
    console.log('  💾 状态追踪：使用 Set 记录折叠深度');
    console.log('  🎯 逻辑简化：代码更易理解和维护');
    console.log('='.repeat(60));
    console.log(allPassed ? '🎉 所有优化测试通过！' : '⚠️  优化需要进一步调整');
    console.log('='.repeat(60));

    return allPassed;
}

// 如果直接运行此脚本
if (require.main === module) {
    try {
        runOptimizedTest();
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

module.exports = { runOptimizedTest, OptimizedGroupCalculator };
