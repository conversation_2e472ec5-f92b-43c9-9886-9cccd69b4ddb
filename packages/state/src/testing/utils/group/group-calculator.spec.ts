import { GroupCalculator } from '../../../utils/group/group-calculator';
import { AITableFieldType, AITableView, AITableViewRecords, SortDirection, AITableGroupField } from '@ai-table/utils';
import { AITable, AITableQueries, FieldModelMap, AITableRowType } from '@ai-table/grid';

describe('GroupCalculator test', () => {
    let mockAITable: jasmine.SpyObj<AITable>;
    let mockRecords: AITableViewRecords;
    let mockActiveView: AITableView;
    let groupCalculator: GroupCalculator;

    beforeEach(() => {
        // 创建具有三级分组的测试数据，使用直观的层级表示
        mockRecords = [
            // 第一个一级分组：1
            {
                _id: 'record1',
                short_id: 'r1',
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1',
                values: {
                    level1: '1', // 第一级分组
                    level2: '1-1', // 第二级分组
                    level3: '1-1-1', // 第三级分组
                    content: '数据1'
                },
                positions: { view1: 1 }
            },
            {
                _id: 'record2',
                short_id: 'r2',
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1',
                values: {
                    level1: '1',
                    level2: '1-1',
                    level3: '1-1-2', // 第三级分组不同
                    content: '数据2'
                },
                positions: { view1: 2 }
            },
            {
                _id: 'record3',
                short_id: 'r3',
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1',
                values: {
                    level1: '1',
                    level2: '1-2', // 第二级分组不同
                    level3: '1-2-1',
                    content: '数据3'
                },
                positions: { view1: 3 }
            },
            // 第二个一级分组：2
            {
                _id: 'record4',
                short_id: 'r4',
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1',
                values: {
                    level1: '2', // 第一级分组不同
                    level2: '2-1',
                    level3: '2-1-1',
                    content: '数据4'
                },
                positions: { view1: 4 }
            }
        ];

        // 创建 mock view
        mockActiveView = {
            _id: 'view1',
            short_id: 'v1',
            name: 'Test View',
            settings: {}
        };

        // 创建 mock AITable
        mockAITable = jasmine.createSpyObj('AITable', ['fieldsMap', 'context']);

        // 设置 mock fieldsMap - 三个分组字段
        const mockFieldsMap = {
            level1: {
                _id: 'level1',
                short_id: 'l1',
                name: '一级分组',
                type: AITableFieldType.text,
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1'
            },
            level2: {
                _id: 'level2',
                short_id: 'l2',
                name: '二级分组',
                type: AITableFieldType.text,
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1'
            },
            level3: {
                _id: 'level3',
                short_id: 'l3',
                name: '三级分组',
                type: AITableFieldType.text,
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1'
            },
            content: {
                _id: 'content',
                short_id: 'ct',
                name: '内容',
                type: AITableFieldType.text,
                created_at: Date.now(),
                updated_at: Date.now(),
                created_by: 'user1',
                updated_by: 'user1'
            }
        };

        mockAITable.fieldsMap.and.returnValue(mockFieldsMap);

        // 设置 mock context
        const mockContext = {
            references: jasmine.createSpy('references').and.returnValue({})
        };
        mockAITable.context = mockContext as any;

        // Mock AITableQueries.getFieldValue
        spyOn(AITableQueries, 'getFieldValue').and.callFake((aiTable, path) => {
            const [recordId, fieldId] = path;
            const record = mockRecords.find((r) => r._id === recordId);
            return record?.values[fieldId];
        });

        // Mock FieldModelMap的compare方法
        spyOn(FieldModelMap[AITableFieldType.text], 'compare').and.callFake((value1: any, value2: any) => {
            if (value1 < value2) return -1;
            if (value1 > value2) return 1;
            return 0;
        });

        // 定义三级分组配置
        const groups: AITableGroupField[] = [
            {
                field_id: 'level1',
                direction: SortDirection.ascending
            },
            {
                field_id: 'level2',
                direction: SortDirection.ascending
            },
            {
                field_id: 'level3',
                direction: SortDirection.ascending
            }
        ];

        // 创建GroupCalculator实例
        groupCalculator = new GroupCalculator(mockAITable, groups);
    });

    // 测试三级分组的线性行计算，使用直观的分组数据表示
    it('should calculate linear rows with three-level grouping using intuitive group data representation (1, 1-1, 1-1-1)', () => {
        const result = groupCalculator.calculateLinearRows(mockRecords);

        // 验证结果结构
        expect(result).toBeDefined();
        expect(result.length).toBeGreaterThan(0);

        // 查找各种类型的行
        const blankRows = result.filter((row) => row.type === AITableRowType.blank);
        const groupRows = result.filter((row) => row.type === AITableRowType.group);
        const recordRows = result.filter((row) => row.type === AITableRowType.record);
        const addRows = result.filter((row) => row.type === AITableRowType.add);

        // 验证包含空白行
        expect(blankRows.length).toBeGreaterThan(0);

        // 验证包含分组行 - 应该有多个分组标签
        expect(groupRows.length).toBeGreaterThan(0);

        // 验证包含记录行 - 应该等于原始记录数
        expect(recordRows.length).toBe(mockRecords.length);

        // 验证包含添加行
        expect(addRows.length).toBeGreaterThan(0);

        // 验证分组层级深度
        const groupRowsWithDepth = groupRows.filter((row) => row.depth !== undefined);
        const depths = groupRowsWithDepth.map((row) => row.depth);

        // 应该有三个不同的深度层级：0(level1), 1(level2), 2(level3)
        const uniqueDepths = [...new Set(depths)];
        expect(uniqueDepths.sort()).toEqual([0, 1, 2]);

        // 验证分组值
        const level1Groups = groupRows.filter((row) => row.fieldId === 'level1');
        const level2Groups = groupRows.filter((row) => row.fieldId === 'level2');
        const level3Groups = groupRows.filter((row) => row.fieldId === 'level3');

        // 应该有2个一级分组：'1', '2'
        expect(level1Groups.length).toBe(2);

        // 应该有3个二级分组：'1-1', '1-2', '2-1'
        expect(level2Groups.length).toBe(3);

        // 应该有4个三级分组：'1-1-1', '1-1-2', '1-2-1', '2-1-1'
        expect(level3Groups.length).toBe(4);

        // 验证记录行的深度应该是3（最深层级）
        recordRows.forEach((row) => {
            expect(row.depth).toBe(3);
        });

        result.forEach((row, index) => {
            if (row.type === AITableRowType.group) {
                const groupRow = row as any;
                console.log(`${index}: Group(depth:${groupRow.depth}) - ${groupRow.fieldId}: ${groupRow.groupValue}`);
            } else if (row.type === AITableRowType.record) {
                console.log(`${index}: Record(depth:${row.depth}) - ${row._id}`);
            } else {
                console.log(`${index}: ${row.type}(depth:${row.depth})`);
            }
        });
    });

    // 测试折叠一级分组功能
    it('should collapse first-level group and hide all sub-groups when using collapsed_group_ids', () => {
        // 定义三级分组配置
        const groups: AITableGroupField[] = [
            {
                field_id: 'level1',
                direction: SortDirection.ascending
            },
            {
                field_id: 'level2',
                direction: SortDirection.ascending
            },
            {
                field_id: 'level3',
                direction: SortDirection.ascending
            }
        ];

        // 首先计算正常的分组结果，获取一级分组的ID
        const normalCalculator = new GroupCalculator(mockAITable, groups);
        const normalResult = normalCalculator.calculateLinearRows(mockRecords);

        // 找到第一个一级分组的ID（应该是 'level1_0_0'，对应 '1' 这个分组）
        const firstLevelGroups = normalResult.filter((row) => row.type === AITableRowType.group && (row as any).fieldId === 'level1');

        expect(firstLevelGroups.length).toBe(2); // 应该有两个一级分组：'1' 和 '2'

        const firstGroupId = (firstLevelGroups[0] as any).groupId;
        expect(firstGroupId).toBe('level1_0_0'); // 验证分组ID格式

        // 创建带折叠状态的GroupCalculator，折叠第一个一级分组
        const collapsedCalculator = new GroupCalculator(mockAITable, groups, [firstGroupId]);
        const collapsedResult = collapsedCalculator.calculateLinearRows(mockRecords);

        // 验证折叠后的结果
        const collapsedGroupRows = collapsedResult.filter((row) => row.type === AITableRowType.group);
        const collapsedRecordRows = collapsedResult.filter((row) => row.type === AITableRowType.record);
        const collapsedAddRows = collapsedResult.filter((row) => row.type === AITableRowType.add);

        // 应该只有一级分组行可见，二级和三级分组被隐藏
        const visibleLevel1Groups = collapsedGroupRows.filter((row) => (row as any).fieldId === 'level1');
        const visibleLevel2Groups = collapsedGroupRows.filter((row) => (row as any).fieldId === 'level2');
        const visibleLevel3Groups = collapsedGroupRows.filter((row) => (row as any).fieldId === 'level3');

        // 一级分组应该都可见（包括折叠的分组标签）
        expect(visibleLevel1Groups.length).toBe(2);

        // 验证第一个一级分组是折叠状态
        const collapsedGroup = visibleLevel1Groups.find((group) => (group as any).groupId === firstGroupId);
        expect(collapsedGroup).toBeDefined();
        expect((collapsedGroup as any).isCollapsed).toBe(true);

        // 二级分组：只有第二个一级分组('2')下的二级分组可见
        // 修复后：被折叠分组的子分组应该被正确隐藏
        console.log('\n=== 折叠功能修复验证 ===');
        console.log('被折叠的分组ID:', firstGroupId);
        console.log('可见的二级分组数量:', visibleLevel2Groups.length);
        visibleLevel2Groups.forEach((group) => {
            const g = group as any;
            console.log(`  二级分组: ${g.groupValue} (ID: ${g.groupId})`);
        });

        // 现在应该只有不属于被折叠分组的子分组可见
        expect(visibleLevel2Groups.length).toBe(1);
        expect((visibleLevel2Groups[0] as any).groupValue).toBe('2-1');

        // 问题确认：filterVisibleGroupTabs 方法的逻辑错误
        // 期望行为：当分组1被折叠时，其所有子分组和记录都应该被隐藏
        // 实际行为：子分组仍然可见，这是 bug

        // 根据用户要求不修改原有代码，我们先记录问题
        console.log('\n=== 问题记录 ===');
        console.log('filterVisibleGroupTabs 方法存在逻辑错误：');
        console.log('1. 只能检查同一记录索引下的父级分组');
        console.log('2. 无法跨记录查找真正的分组层级关系');
        console.log('3. 导致被折叠分组的子分组仍然显示');

        console.log('\n应该被隐藏但仍然可见的分组：');
        const shouldHideButVisible = visibleLevel2Groups.filter((group) => {
            const g = group as any;
            return g.groupValue.startsWith('1-'); // 属于被折叠分组1的子分组
        });
        shouldHideButVisible.forEach((group) => {
            const g = group as any;
            console.log(`  应该隐藏: ${g.groupValue} (ID: ${g.groupId})`);
        });

        // 暂时注释掉失败的断言，等待修复
        // expect(visibleLevel2Groups.length).toBe(1);
        // expect((visibleLevel2Groups[0] as any).groupValue).toBe('2-1');

        console.log('可见的三级分组数量:', visibleLevel3Groups.length);
        visibleLevel3Groups.forEach((group) => {
            const g = group as any;
            console.log(`  三级分组: ${g.groupValue} (ID: ${g.groupId})`);
        });

        // 修复后：只有不属于被折叠分组的三级分组可见
        expect(visibleLevel3Groups.length).toBe(1);
        expect((visibleLevel3Groups[0] as any).groupValue).toBe('2-1-1');

        // 记录行：只有第二个一级分组('2')下的记录可见
        expect(collapsedRecordRows.length).toBe(1);
        expect(collapsedRecordRows[0]._id).toBe('record4'); // 只有属于'2'分组的记录

        // 添加行：只有第二个一级分组('2')下的添加行可见
        console.log('可见的添加行数量:', collapsedAddRows.length);
        expect(collapsedAddRows.length).toBe(1); // 修复后应该只有1个

        // 验证折叠分组下的所有子内容都被隐藏
        const level1Value1Records = mockRecords.filter((record) => record.values['level1'] === '1');
        expect(level1Value1Records.length).toBe(3); // 原本应该有3条记录属于'1'分组

        // 但在折叠结果中，这3条记录都不应该出现
        const visibleLevel1Value1Records = collapsedRecordRows.filter((record) => {
            const originalRecord = mockRecords.find((r) => r._id === record._id);
            return originalRecord?.values['level1'] === '1';
        });
        expect(visibleLevel1Value1Records.length).toBe(0);

        console.log('collapsedResult=', collapsedResult);
        // debugger; // 注释掉 debugger
        collapsedResult.forEach((row, index) => {
            if (row.type === AITableRowType.group) {
                const groupRow = row as any;
                console.log(
                    `${index}: Group(depth:${groupRow.depth}, collapsed:${groupRow.isCollapsed}) - ${groupRow.fieldId}: ${groupRow.groupValue} [ID: ${groupRow.groupId}]`
                );
            } else if (row.type === AITableRowType.record) {
                console.log(`${index}: Record(depth:${row.depth}) - ${row._id}`);
            } else {
                console.log(`${index}: ${row.type}(depth:${row.depth})`);
            }
        });
    });
});
