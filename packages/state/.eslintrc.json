{"extends": "../../.eslintrc.json", "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["packages/state/tsconfig.lib.json", "packages/state/tsconfig.spec.json"], "createDefaultProgram": true}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "ai-table", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "ai-table", "style": "kebab-case"}]}}]}