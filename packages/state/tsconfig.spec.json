/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
    "extends": "../../tsconfig.json",
    "compilerOptions": {
        "outDir": "../../out-tsc/spec",
        "types": ["jasmine"],
        "baseUrl": "../../",
        "moduleResolution": "node",
        "skipLibCheck": true,
        "paths": {
            "@ai-table/grid": ["packages/grid/src/public-api"],
            "@ai-table/grid/*": ["packages/grid/src/*"],
            "@ai-table/state": ["packages/state/src/public-api"],
            "@ai-table/state/*": ["packages/state/src/*"],
            "@ai-table/utils": ["packages/utils/src/public-api"],
            "@ai-table/utils/*": ["packages/utils/src/*"]
        }
    },
    "files": ["src/test.ts"],
    "include": ["src/**/*.spec.ts", "**/*.d.ts"]
}
