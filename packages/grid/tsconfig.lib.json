/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
    "extends": "../../tsconfig.json",
    "compilerOptions": {
        "rootDir": "src",
        "outDir": "../../out-tsc/lib",
        "target": "es2020",
        "declaration": true,
        "declarationMap": true,
        "inlineSources": true,
        "types": [],
        "lib": [
            "dom",
            "es2022"
        ],
        "baseUrl": "../../",
        "paths": {
            "@ai-table/utils": [
                "dist/utils"
            ],
            "@ai-table/utils/*": [
                "dist/utils/*"
            ]
        }
    },
    "angularCompilerOptions": {
        "skipTemplateCodegen": true,
        "strictMetadataEmit": true,
        "enableResourceInlining": true
    },
    "exclude": [
        "test.ts",
        "core/**/**",
        "**/*.spec.ts"
    ]
}