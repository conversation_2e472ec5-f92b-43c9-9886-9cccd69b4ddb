@if (field()) {
    @for (menu of fieldMenus; track index; let index = $index) {
        @if ((menu.hidden && !menu.hidden(aiTable, field)) || !menu.hidden) {
            @if (menu.type === 'divider') {
                <thy-divider [thyStyle]="'solid'"></thy-divider>
            } @else {
                @let disabled = !!(menu.disabled && menu.disabled(aiTable, field));
                @let isRemoveField = menu.type === 'removeField';
                @let customComponent = getCustomComponent(menu);
                <a
                    thyDropdownMenuItem
                    href="javascript:;"
                    [ngClass]="{ 'remove-field': isRemoveField && !disabled }"
                    (click)="execute(menu)"
                    [thyDisabled]="disabled"
                >
                    @if (customComponent) {
                        <ng-container *ngComponentOutlet="customComponent; inputs: { field: field(), menu: menu }"> </ng-container>
                    } @else {
                        <thy-icon [thyIconName]="menu.icon!"></thy-icon>
                        <span>{{ getMenuName(menu, field()) }}</span>
                    }
                </a>
            }
        }
    }
}
