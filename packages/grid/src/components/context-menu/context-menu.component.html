@for (menu of menuItems(); track $index) {
    @if ((menu.hidden && !menu.hidden(aiTable(), targetName(), position())) || !menu.hidden) {
        @if (menu.type === 'divider') {
            <thy-divider thyStyle="solid"></thy-divider>
        } @else {
            @let disabled = !!(menu.disabled && menu.disabled(aiTable(), targetName(), position()));
            @let isRemoveRecords = menu.type === 'removeRecords';
            @let isPreventClearSelection =
                menu.type === 'copyCells' ||
                menu.type === 'pasteCells' ||
                menu.type === 'removeRecords' ||
                menu.type === 'insertUpwardRecords' ||
                menu.type === 'insertDownwardRecords';

            <a
                thyDropdownMenuItem
                href="javascript:;"
                [ngClass]="{
                    'remove-record': isRemoveRecords && !disabled,
                    'ai-table-prevent-clear-selection': isPreventClearSelection && !disabled
                }"
                draggable="false"
                (click)="execute(menu)"
                [thyDisabled]="disabled"
            >
                <thy-icon thyDropdownMenuItemIcon [thyIconName]="menu.icon!"></thy-icon>
                @if (menu?.isInputNumber) {
                    <span thyDropdownMenuItemName class="d-flex align-items-center">
                        {{ menu.name }}
                        <thy-input-number
                            #inputNumber
                            class="mx-2"
                            thySize="sm"
                            [(ngModel)]="menu.count"
                            [thyStep]="1"
                            [thyMin]="1"
                            [thyMax]="maxCount()"
                            (click)="inputNumberFocus($event)"
                            (thyEnter)="itemEnterHandle($event, menu)"
                            thyStopPropagation
                        ></thy-input-number>
                        {{ menu.nameSuffix }}
                    </span>
                } @else {
                    <span thyDropdownMenuItemName>{{ menu.name }}</span>
                }
                <span thyDropdownMenuItemMeta class="text-desc">{{ menu.shortcutKey }}</span>
            </a>
        }
    }
}
