<thy-select
    [(ngModel)]="modelValue"
    [thyAutoExpand]="true"
    [thyAllowClear]="true"
    [thyPlaceHolder]="''"
    [thyPreset]="preset()"
    [thyMode]="field().settings.is_multiple ? 'multiple' : ''"
    (ngModelChange)="onModelChange($event)"
    (thyOnExpandStatusChange)="onOpenChange($event)"
>
    <ng-template #selectedDisplay let-option>
        <select-option [field]="field()" [displayOption]="option"></select-option>
    </ng-template>
    @for (option of selectOptions(); track option._id) {
        <thy-option
            [thyValue]="option._id"
            [hidden]="!!option.is_disabled"
            [thyRawValue]="option"
            [thyShowOptionCustom]="true"
            [thyLabelText]="option.text"
        >
            <select-option [field]="field()" [displayOption]="option"></select-option>
        </thy-option>
    }
</thy-select>
