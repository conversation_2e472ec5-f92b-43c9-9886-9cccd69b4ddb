@use 'ngx-tethys/styles/variables.scss';
@use '../cell-editor.variables.scss' as cellEditorVariables;

.ai-table-link-editor {
    display: block;
    height: 100%;
    .link-input-group {
        border: none;
        .link-input {
            color: variables.$primary;
            padding-left: cellEditorVariables.$cell-editor-padding-left !important;
            line-height: variables.$line-height-lg;
        }
        .input-group-suffix {
            margin-right: 8px;
        }
        .form-control {
            min-height: auto;
        }
    }
}
