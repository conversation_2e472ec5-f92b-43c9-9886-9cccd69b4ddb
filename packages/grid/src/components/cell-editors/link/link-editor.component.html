<thy-input-group class="link-input-group h-100" thySize="lg">
    <input
        #inputElement
        class="h-100"
        class="link-input"
        thyInput
        thySize="md"
        [thyAutofocus]="true"
        [(ngModel)]="text"
        (blur)="blur($event)"
        (thyEnter)="updateValue()"
    />
    <ng-template #suffix>
        <a
            thyAction
            thyIcon="link-insert"
            [thyTooltip]="linkTooltip()"
            class="font-size-base edit-icon"
            [class.active]="isOpened"
            href="javascript:;"
            (click)="openEdit()"
        ></a>
    </ng-template>
</thy-input-group>
