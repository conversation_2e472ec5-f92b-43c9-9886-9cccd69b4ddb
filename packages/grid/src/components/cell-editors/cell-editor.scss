@use 'ngx-tethys/styles/variables.scss';
@use './cell-editor.variables.scss' as cellEditorVariables;
@use './link/link-editor.component.scss';

.grid-cell-editor {
    .thy-popover-container {
        box-shadow: none;
        border: none;
        border-radius: 0;
    }
    &.thy-popover-panel {
        padding: 0;
    }
    .form-control {
        border: 2px solid variables.$primary;
        min-height: 45px;
        border-radius: 0;
        padding-top: cellEditorVariables.$cell-editor-padding-top;
        padding-bottom: cellEditorVariables.$cell-editor-padding-top;
        padding-left: cellEditorVariables.$cell-editor-padding-left;
    }
}

.text-cell-editor {
    display: block;
    textarea {
        border: none;
        line-height: 24px;
        resize: none;
        scrollbar-width: none;
        overflow-y: scroll;
        border-radius: 0;

        &::-webkit-scrollbar {
            width: 0;
            height: 0;
        }
    }
}

.select-cell-editor {
    .thy-select-custom:focus .form-control-custom:not(.disabled) {
        border: 2px solid variables.$primary;
        outline: none;
    }
    .thy-select .select-control-multiple .select-option {
        font-size: variables.$font-size-sm;
    }
}

.number-cell-editor {
    .thy-input-number {
        .input-number-handler-wrap {
            height: calc(100% - 4px);
            top: 2px;
            right: 2px;
        }
    }
}
