@use 'ngx-tethys/styles/variables.scss';

.ai-table-drag-container {
    position: absolute;
    left: 0;
    top: 0;
    width: calc(100% - 18px);
    height: calc(100% - 18px);
    display: none;
    .rect {
        position: absolute;
        background-color: variables.$primary;
        opacity: 0.1;
    }
    .auxiliary-line {
        position: absolute;
        background-color: variables.$primary;
    }
}