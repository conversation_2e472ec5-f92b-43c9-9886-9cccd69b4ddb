@if (displayOption(); as displayOption) {
    @switch (optionStyle()) {
        @case (AITableSelectOptionStyle.dot) {
            @if (displayOption.bg_color || displayOption.color; as color) {
                <thy-dot [thyColor]="color" [thySize]="'md'" class="mr-2"></thy-dot>
            }
            <span thyFlexibleText [thyTooltipContent]="displayOption.text">{{ displayOption.text }}</span>
        }
        @case (AITableSelectOptionStyle.tag) {
            @if (displayOption.bg_color || displayOption.color || 'default'; as color) {
                <thy-tag thyTheme="fill" thyShape="pill" [thyColor]="color">
                    @if (displayOption['icon']) {
                        <thy-icon class="text-white" [thyIconName]="displayOption['icon']"></thy-icon>
                    }
                    <span thyFlexibleText [thyTooltipContent]="displayOption.text">{{ displayOption.text }}</span>
                </thy-tag>
            }
        }
        @case (AITableSelectOptionStyle.piece) {
            @if (displayOption.bg_color || displayOption.color; as color) {
                <thy-dot thyShape="square" [thyColor]="color" [thySize]="'md'" class="mr-2"></thy-dot>
            }
            <span thyFlexibleText [thyTooltipContent]="displayOption.text">{{ displayOption.text }}</span>
        }
        @default {
            @if (displayOption['icon']) {
                <thy-icon [thyIconName]="displayOption['icon']" [style.color]="displayOption.color"></thy-icon>
            }
            <span thyFlexibleText [thyTooltipContent]="displayOption.text">{{ displayOption.text }}</span>
        }
    }
}
