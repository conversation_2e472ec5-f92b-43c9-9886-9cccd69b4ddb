<form thyForm name="createPropertyForm" [thyFormValidatorConfig]="validatorConfig()" thyLayout="vertical">
    <thy-form-group thyLabelRequired [thyLabelText]="i18nTexts().columnName">
        <thy-input-group>
            <input
                thyInput
                [thyAutofocus]="true"
                name="fieldName"
                [maxlength]="fieldMaxLength"
                [(ngModel)]="aiEditField().name"
                (ngModelChange)="nameChange($event)"
                required
                [placeholder]="i18nTexts().columnNamePlaceholder"
                [thyUniqueCheck]="checkUniqueName"
            />
            <ng-template #suffix>
                <thy-input-count></thy-input-count>
            </ng-template>
        </thy-input-group>
    </thy-form-group>
    <thy-form-group [thyLabelText]="i18nTexts().fieldType">
        <div class="thy-dropdown-menu py-0">
            <div class="ml-n5 mr-n5">
                <span thyDropdownMenuItem [thyDropdown]="menu" thyTrigger="hover" thyPlacement="right" (click)="fieldTypeClick($event)">
                    <thy-icon thyDropdownMenuItemIcon [thyIconName]="selectedFieldOption().icon"></thy-icon>
                    <span thyDropdownMenuItemName>{{ selectedFieldOption().name }}</span>
                    <thy-icon thyDropdownMenuItemExtendIcon thyIconName="angle-right" class="text-desc"></thy-icon>
                </span>
            </div>
        </div>

        @if (selectedFieldOption().type === aITableFieldType.member) {
            <div class="d-flex justify-content-between mt-3">
                {{ i18nTexts().allowMultipleMembers }}
                <thy-switch
                    name="isMultipleMember"
                    [(ngModel)]="isMultipleMember"
                    (ngModelChange)="multipleMemberChange()"
                    thySize="sm"
                ></thy-switch>
            </div>
        }
    </thy-form-group>
    @if (aiExternalTemplate()) {
        <ng-container *ngTemplateOutlet="aiExternalTemplate()"></ng-container>
    }
    <thy-form-group-footer thyAlign="right">
        <button thyButton="link-secondary" (click)="cancel()" thySize="sm">{{ i18nTexts().cancel }}</button>
        <button thyButton="primary" (thyFormSubmit)="editFieldProperty()" thySize="sm">{{ i18nTexts().confirm }}</button>
    </thy-form-group-footer>
</form>

<thy-dropdown-menu #menu>
    <div class="ai-table-field-type-menu">
        <thy-dropdown-menu-group [thyTitle]="i18nTexts().base">
            @for (item of fieldOptions().base; track $index) {
                <a
                    thyDropdownMenuItem
                    href="javascript:;"
                    [ngClass]="{
                        active: (item | fieldIsSameOption: aiEditField())
                    }"
                    (click)="selectFieldType(item)"
                >
                    <thy-icon [thyIconName]="item.icon!"></thy-icon>
                    <span thyDropdownMenuItemName>{{ item.name }}</span>
                </a>
            }
        </thy-dropdown-menu-group>
        <thy-dropdown-menu-group [thyTitle]="i18nTexts().advanced">
            @for (item of fieldOptions().advanced; track $index) {
                <a
                    thyDropdownMenuItem
                    href="javascript:;"
                    [ngClass]="{
                        active: (item | fieldIsSameOption: aiEditField())
                    }"
                    (click)="selectFieldType(item)"
                >
                    <thy-icon [thyIconName]="item.icon!"></thy-icon>
                    <span thyDropdownMenuItemName>{{ item.name }}</span>
                </a>
            }
        </thy-dropdown-menu-group>
    </div>
</thy-dropdown-menu>
