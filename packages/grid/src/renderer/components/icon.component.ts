import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { StageConfig } from 'konva/lib/Stage';
import { KoContainer, KoEventObject } from '../../angular-konva';
import { KoShape } from '../../angular-konva/components/shape.component';
import { Check, Colors, DEFAULT_ICON_SIZE, RowDragPath, Unchecked } from '../../constants';
import { AITableCheckType, AITableIconConfig } from '../../types';
import { DragType } from '@ai-table/utils';

@Component({
    selector: 'ai-table-icon',
    template: `
        <ko-group [config]="groupConfig()" (koClick)="koClick.emit($event)">
            <ko-rect [config]="squareShapeConfig()"></ko-rect>
            <ko-path [config]="iconConfig()"></ko-path>
        </ko-group>
    `,
    imports: [<PERSON><PERSON>ontaine<PERSON>, Ko<PERSON>ha<PERSON>],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AITableIcon {
    config = input.required<AITableIconConfig>();

    koClick = output<KoEventObject<MouseEvent>>();

    groupConfig = computed<Partial<StageConfig>>(() => {
        const { x, y, listening } = this.config();
        return { x, y, listening };
    });

    squareShapeConfig = computed(() => {
        const {
            name,
            backgroundWidth,
            backgroundHeight,
            size = DEFAULT_ICON_SIZE,
            strokeWidth = 1,
            background,
            cornerRadius,
            opacity
        } = this.config();
        return {
            name,
            width: backgroundWidth || size,
            height: backgroundHeight || size,
            strokeWidth: strokeWidth,
            fill: background || Colors.transparent,
            cornerRadius,
            opacity
        };
    });

    iconConfig = computed(() => {
        const {
            type,
            data,
            backgroundWidth,
            backgroundHeight,
            size = DEFAULT_ICON_SIZE,
            stroke,
            strokeWidth = 1,
            scaleX,
            scaleY,
            offsetX,
            offsetY,
            rotation,
            fill = Colors.gray600,
            transformsEnabled = 'position',
            disabled
        } = this.config();

        let pathData = data;

        switch (type) {
            case AITableCheckType.checked:
                pathData = Check;
                break;
            case AITableCheckType.unchecked:
                pathData = Unchecked;
                break;
            case DragType.record:
                pathData = RowDragPath;
                break;
        }
        return {
            x: backgroundWidth && (backgroundWidth - size * (scaleX || 1)) / 2,
            y: backgroundHeight && (backgroundHeight - size * (scaleY || 1)) / 2,
            data: pathData,
            width: size,
            height: size,
            fill: disabled ? Colors.gray300 : fill,
            offsetX,
            offsetY,
            scaleX,
            scaleY,
            rotation,
            stroke,
            strokeWidth,
            transformsEnabled,
            perfectDrawEnabled: false,
            listening: false
        };
    });
}
