<ko-stage
    [config]="stageConfig()"
    (koMousemove)="stageMousemove($event)"
    (koMousedown)="stageMousedown($event)"
    (koMouseup)="stageMouseup($event)"
    (koContextmenu)="stageContextmenu($event)"
    (koClick)="stageClick($event)"
    (koDblclick)="stageDblclick($event)"
    (koMouseleave)="stageMouseleave($event)"
    (koWheel)="stageWheel($event)"
>
    <ko-layer>
        <ko-group [config]="gridGroupConfig()">
            <!-- 右侧非固定列的区域 -->
            <ko-group [config]="commonGroupConfig()">
                <!-- 右侧x,y滚动区域的 cell -->
                <ko-group #commonOffsetGroup [config]="offsetConfig()">
                    <ai-table-cells [config]="cellsConfig()"></ai-table-cells>
                    <ai-table-placeholder-cells [config]="cellsConfig()"></ai-table-placeholder-cells>
                    <ai-table-cover-cell-entry [config]="cellsConfig()"></ai-table-cover-cell-entry>
                    <ai-table-groups [config]="cellsConfig()"></ai-table-groups>
                </ko-group>

                <!-- 右侧表头head 和 新增列 + 区域 -->
                <ko-group [config]="offsetXConfig()">
                    <ai-table-column-heads [config]="columnHeadFieldConfig()"></ai-table-column-heads>
                    <ai-table-add-field [config]="columnHeadFieldConfig()"></ai-table-add-field>
                </ko-group>
            </ko-group>

            <!-- 左侧固定列 y滚动区域的cell -->
            <ko-group [config]="frozenCommonGroupConfig()">
                <ko-group [config]="offsetYConfig()">
                    <ai-table-frozen-cells [config]="cellsConfig()"></ai-table-frozen-cells>
                    <ai-table-other-rows [config]="cellsConfig()"></ai-table-other-rows>
                    @if (!hiddenIndexColumn()) {
                        <ai-table-hover-row-heads [config]="cellsConfig()"></ai-table-hover-row-heads>
                    }
                    <ai-table-frozen-groups [config]="cellsConfig()"></ai-table-frozen-groups>
                    <ai-table-frozen-placeholder-cells [config]="cellsConfig()"></ai-table-frozen-placeholder-cells>
                </ko-group>
            </ko-group>

            <!-- 左侧固定列 表头 + 表头右侧分割阴影 -->
            <ko-group>
                <ai-table-frozen-column-heads [config]="columnFrozenHeadFieldConfig()"></ai-table-frozen-column-heads>
                <ai-table-shadow [config]="fieldHeadShadowConfig()"></ai-table-shadow>
            </ko-group>

            <ko-group [config]="attachGroupConfig()">
                <ko-group [config]="offsetConfig()">
                    @if (activeCellBorderConfig().activeCellBorder) {
                        <ko-rect [config]="activeCellBorderConfig().activeCellBorder!"></ko-rect>
                    }
                    @if (showExpandCellBorder().expandCellBorder) {
                        <ai-table-cover-cell-entry [config]="cellsConfig()" [onlyDisplayBorder]="true"></ai-table-cover-cell-entry>
                    }
                    @if (!isLastSelectedCellInFrozenColumn()) {
                        <ai-table-fill-handle [config]="fillHandleConfig()"></ai-table-fill-handle>
                    }
                </ko-group>
            </ko-group>
            <ko-group [config]="frozenCoverAttachGroupConfig()">
                <ko-group #frozenCoverAttachOffsetGroup [config]="offsetYConfig()">
                    <ai-table-cover-cell-entry [config]="cellsConfig()"></ai-table-cover-cell-entry>
                </ko-group>
            </ko-group>

            <ko-group [config]="frozenAttachGroupConfig()">
                <ko-group [config]="offsetYConfig()">
                    @if (activeCellBorderConfig().frozenActiveCellBorder) {
                        <ko-rect [config]="activeCellBorderConfig().frozenActiveCellBorder!"></ko-rect>
                    }
                    @if (showExpandCellBorder().frozenExpandCellBorder) {
                        <ai-table-cover-cell-entry [config]="cellsConfig()" [onlyDisplayBorder]="true"></ai-table-cover-cell-entry>
                    }
                    @if (isLastSelectedCellInFrozenColumn()) {
                        <ai-table-fill-handle [config]="fillHandleConfig()"></ai-table-fill-handle>
                    }
                </ko-group>
            </ko-group>
        </ko-group>

        <ko-group [config]="statGroupConfig()">
            <ai-table-background [config]="columnFieldStatsBgConfig()"></ai-table-background>
            <ko-group>
                <ai-table-column-stats
                    [config]="columnFrozenFieldStatsConfig()"
                    (hover)="onStatContainerHover($event)"
                ></ai-table-column-stats>
            </ko-group>
            <ko-group [config]="statCommonGroupConfig()">
                <ko-group [config]="offsetXConfig()">
                    <ai-table-column-stats
                        [config]="columnFieldStatsConfig()"
                        (hover)="onStatContainerHover($event)"
                    ></ai-table-column-stats>
                </ko-group>
            </ko-group>
            <ko-group>
                <ai-table-shadow [config]="statShadowConfig()"></ai-table-shadow>
            </ko-group>
        </ko-group>
    </ko-layer>
</ko-stage>

<ng-content></ng-content>
