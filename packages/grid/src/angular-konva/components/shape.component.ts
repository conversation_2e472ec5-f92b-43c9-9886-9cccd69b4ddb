import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    OnDestroy,
    OnInit,
    Output,
    effect,
    inject,
    input
} from '@angular/core';
import Konva from 'konva';
import { FastLayer } from 'konva/lib/FastLayer';
import { Group } from 'konva/lib/Group';
import { Layer } from 'konva/lib/Layer';
import { Shape } from 'konva/lib/Shape';
import { Arc } from 'konva/lib/shapes/Arc';
import { Arrow } from 'konva/lib/shapes/Arrow';
import { Circle } from 'konva/lib/shapes/Circle';
import { Ellipse } from 'konva/lib/shapes/Ellipse';
import { Image } from 'konva/lib/shapes/Image';
import { Label, Tag } from 'konva/lib/shapes/Label';
import { Line } from 'konva/lib/shapes/Line';
import { Path } from 'konva/lib/shapes/Path';
import { Rect } from 'konva/lib/shapes/Rect';
import { RegularPolygon } from 'konva/lib/shapes/RegularPolygon';
import { Ring } from 'konva/lib/shapes/Ring';
import { Sprite, SpriteConfig } from 'konva/lib/shapes/Sprite';
import { Star } from 'konva/lib/shapes/Star';
import { Text } from 'konva/lib/shapes/Text';
import { TextPath } from 'konva/lib/shapes/TextPath';
import { Transformer } from 'konva/lib/shapes/Transformer';
import { Wedge } from 'konva/lib/shapes/Wedge';
import { KoComponent, KoEventObject, KoShapeConfigTypes, KoShapeTypes } from '../interfaces';
import { applyNodeProps, createListener, getName, updatePicture } from '../utils';
import { KO_CONTAINER_TOKEN } from './container.token';

@Component({
    selector:
        'ko-shape, ko-circle, ko-label, ko-rect, ko-ellipse, ko-wedge, ko-line, ko-sprite, ko-image, ko-text, ko-text-path, ko-star, ko-ring, ko-arc, ko-tag, ko-path, ko-regular-polygon, ko-arrow, ko-transformer',
    template: `<ng-content></ng-content>`,
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class KoShape implements OnInit, AfterViewInit, OnDestroy, KoComponent {
    config = input<KoShapeConfigTypes>();

    @Output() koMouseover = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koMousemove = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koMouseout = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koMouseenter = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koMouseleave = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koMousedown = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koMouseup = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koWheel = new EventEmitter<KoEventObject<WheelEvent>>();

    @Output() koContextmenu = new EventEmitter<KoEventObject<PointerEvent>>();

    @Output() koClick = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koDblclick = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koTouchstart = new EventEmitter<KoEventObject<TouchEvent>>();

    @Output() koTouchmove = new EventEmitter<KoEventObject<TouchEvent>>();

    @Output() koTouchend = new EventEmitter<KoEventObject<TouchEvent>>();

    @Output() koTap = new EventEmitter<KoEventObject<TouchEvent>>();

    @Output() koDbltap = new EventEmitter<KoEventObject<TouchEvent>>();

    @Output() koDragstart = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koDragmove = new EventEmitter<KoEventObject<MouseEvent>>();

    @Output() koDragend = new EventEmitter<KoEventObject<MouseEvent>>();

    private elementRef = inject(ElementRef<HTMLElement>);

    private container = inject(KO_CONTAINER_TOKEN, { skipSelf: true });

    private cacheProps: KoShapeConfigTypes = {};

    private nameNode: keyof typeof KoShapeTypes | 'Shape' | 'Sprite' = getName(this.elementRef.nativeElement.localName) as
        | keyof typeof KoShapeTypes
        | 'Shape'
        | 'Sprite';

    private _node!:
        | Shape
        | Arc
        | Arrow
        | Circle
        | Ellipse
        | Image
        | Label
        | Tag
        | Line
        | Path
        | Rect
        | RegularPolygon
        | Ring
        | Sprite
        | Star
        | Text
        | TextPath
        | Transformer
        | Wedge
        | Group
        | Layer
        | FastLayer;

    constructor() {
        effect(() => {
            if (this.config()) {
                this.updateNode(this.config()!);
            } else {
                this.updateNode({});
            }
        });
    }

    public getNode():
        | Shape
        | Arc
        | Arrow
        | Circle
        | Ellipse
        | Image
        | Label
        | Tag
        | Line
        | Path
        | Rect
        | RegularPolygon
        | Ring
        | Sprite
        | Star
        | Text
        | TextPath
        | Transformer
        | Wedge
        | Group
        | Layer
        | FastLayer {
        return this._node;
    }

    ngOnInit(): void {
        this.initKonva();
    }

    private initKonva(): void {
        if (!this._node) {
            this._node = new Shape();
        }
        if (this.nameNode === 'Shape') {
            this._node = new Shape();
        } else if (this.nameNode === 'Sprite') {
            this._node = new Sprite(this.config() as SpriteConfig);
        } else {
            this._node = new Konva[this.nameNode](undefined);
        }

        const animationStage = this._node.to.bind(this._node);
        this._node.to = (newConfig: KoShapeConfigTypes): void => {
            animationStage(newConfig);
            setTimeout(() => {
                Object.keys(this._node.attrs).forEach((key) => {
                    if (typeof this._node.attrs[key] !== 'function') {
                        this.config()![key] = this._node.attrs[key];
                    }
                });
            }, 200);
        };
    }

    private updateNode(config: KoShapeConfigTypes): void {
        if (!this._node) return;
        const props = {
            ...config,
            ...createListener(this)
        };
        applyNodeProps(this, props, this.cacheProps);
        this.cacheProps = props;
    }

    ngAfterViewInit(): void {
        const container = this.container.getNode();
        container.add(<Layer>this.getNode());
        updatePicture(this.container.getNode());
    }

    ngOnDestroy(): void {
        this._node?.destroy();
    }
}
