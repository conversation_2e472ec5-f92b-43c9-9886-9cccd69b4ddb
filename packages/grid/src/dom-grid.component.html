<div class="grid-header d-flex">
    <div class="grid-column-checkbox grid-cell grid-checkbox">
        <label thyCheckbox thyLabelText="" [ngModel]="isSelectedAll()" (ngModelChange)="toggleSelectAll($event)"></label>
    </div>
    @for (field of gridData().fields; track field._id) {
        <div
            class="grid-cell grid-field"
            #fieldAction
            [attr.fieldId]="field._id"
            [ngClass]="{ highlight: aiTable.selection().selectedFields.has(field._id) }"
            [ngStyle]="{ width: gridData().fieldsSizeMap[field._id] + 'px' }"
        >
            <span class="text-truncate">
                <thy-icon [thyIconName]="field.icon!" class="mr-2 text-muted"></thy-icon>
                <span>{{ field.name }}</span>
            </span>
            <a
                href="javascript:;"
                class="grid-field-action"
                thyAction
                thyIcon="more-vertical"
                (click)="openFieldMenu($event, field, fieldAction)"
            >
            </a>
        </div>
    }
    <div class="grid-column-blank cursor-pointer" #gridColumnBlank (click)="addField(gridColumnBlank)">
        <thy-icon thyIconName="plus"></thy-icon>
    </div>
</div>
<div class="grid-body d-flex">
    @for (record of gridData().records; track record._id; let index = $index) {
        <div class="grid-row d-flex" [ngClass]="{ highlight: (record._id | isSelectRecord: aiTable.selection()) }">
            <div class="grid-row-index grid-checkbox">
                <label
                    [ngClass]="(record._id | isSelectRecord: aiTable.selection()) ? 'checked-box' : 'unchecked-box'"
                    thyCheckbox
                    thyLabelText=""
                    [ngModel]="record._id | isSelectRecord: aiTable.selection()"
                    (ngModelChange)="toggleSelectRecord(record._id)"
                ></label>
                <span [ngClass]="(record._id | isSelectRecord: aiTable.selection()) ? 'grid-row-no-number' : 'grid-row-number'">
                    {{ index + 1 }}
                </span>
            </div>
            @for (field of gridData().fields; track field._id) {
                <!-- [ngClass]="{
                        highlight: aiTable.selection().selectedCells.has(record._id) || aiTable.selection().selectedFields.has(field._id),
                        selected: aiTable.selection().selectedCells.get(record._id)?.hasOwnProperty(field._id)
                    }" -->
                <div
                    #cell
                    class="grid-cell"
                    [attr.type]="[field.type]"
                    [attr.fieldId]="[field._id]"
                    [attr.recordId]="[record._id]"
                    [ngStyle]="{ width: gridData().fieldsSizeMap[field._id] + 'px' }"
                >
                    @switch (field.type) {
                        @case (AITableFieldType.select) {
                            @let fieldValue = record.values[field._id];
                            @let settings = field.settings! | selectSetting;
                            @let options = settings['options'];
                            @let optionStyle = settings['option_style'] || AITableSelectOptionStyle.tag;
                            @let isTagStyle = optionStyle === AITableSelectOptionStyle.tag;

                            @if (!settings['is_multiple'] && fieldValue | selectOption: options; as selectedOption) {
                                @if (isTagStyle) {
                                    <select-option class="mb-1 mr-1" [field]="field" [displayOption]="selectedOption"></select-option>
                                } @else {
                                    <div thyTag class="mb-1 mr-1">
                                        <select-option [field]="field" [displayOption]="selectedOption"></select-option>
                                    </div>
                                }
                            } @else {
                                @let maxShowCount = 2;

                                <div class="d-flex">
                                    @if (fieldValue | selectOptions: options; as selectedOptions) {
                                        @for (option of selectedOptions; track option!._id; let i = $index) {
                                            @if (i + 1 <= maxShowCount) {
                                                @if (isTagStyle) {
                                                    <select-option
                                                        class="mb-1 mr-1"
                                                        [field]="field"
                                                        [displayOption]="option!"
                                                    ></select-option>
                                                } @else {
                                                    <div thyTag class="mb-1 mr-1">
                                                        <select-option [field]="field" [displayOption]="option!"></select-option>
                                                    </div>
                                                }
                                            }
                                        }

                                        @let selectedLength = selectedOptions.length || 0;
                                        @if (selectedOptions && maxShowCount < selectedLength) {
                                            @let shape = isTagStyle ? 'pill' : 'rectangle';
                                            @let isHidden = maxShowCount >= selectedLength;

                                            <thy-tag
                                                class="cursor-pointer"
                                                [class.multi-property-value-hidden]="isHidden"
                                                [thyShape]="shape"
                                            >
                                                <span class="text-truncate"> +{{ selectedLength - maxShowCount }} </span>
                                            </thy-tag>
                                        }
                                    }
                                </div>
                            }
                        }
                        @case (AITableFieldType.date) {
                            {{ record.values[field._id].timestamp | thyDatePickerFormat }}
                        }
                        @case (AITableFieldType.updatedAt) {
                            <div class="d-block user-select-none">
                                <span class="text-truncate">
                                    {{ record.values[field._id] | thyDatePickerFormat: 'yyyy-MM-dd HH:mm' }}
                                </span>
                            </div>
                        }
                        @case (AITableFieldType.createdAt) {
                            <div class="d-block user-select-none">
                                <span class="text-truncate">
                                    {{ record.values[field._id] | thyDatePickerFormat: 'yyyy-MM-dd HH:mm' }}
                                </span>
                            </div>
                        }
                        @case (AITableFieldType.rate) {
                            <thy-rate [ngModel]="record.values[field._id]"></thy-rate>
                        }
                        @case (AITableFieldType.link) {
                            <a
                                class="d-block"
                                target="_blank"
                                [href]="record.values[field._id]?.url"
                                thyStopPropagation
                                thyFlexibleText
                                [thyTooltipContent]="record.values[field._id]?.text"
                            >
                                {{ record.values[field._id]?.text }}
                            </a>
                        }
                        @case (AITableFieldType.progress) {
                            <thy-progress
                                class="w-100"
                                [thyValue]="record.values[field._id] || 0"
                                [thySize]="record.values[field._id]?.config?.size || 'md'"
                                [thyMax]="record.values[field._id]?.config?.max || 100"
                                [thyType]="record.values[field._id]?.config?.progressType || 'success'"
                            >
                                <span> {{ record.values[field._id] || 0 }}{{ record.values[field._id]?.config?.suffix || '%' }} </span>
                            </thy-progress>
                        }
                        @case (AITableFieldType.member) {
                            @let settings = field.settings! | memberSetting;

                            @if (!settings!['is_multiple']) {
                                @let recordValues = record.values[field._id] | user: aiReferences()!;

                                @if (recordValues && recordValues.length) {
                                    <thy-avatar
                                        [thyName]="recordValues[0].display_name!"
                                        [thySrc]="recordValues[0].avatar!"
                                        thySize="xs"
                                        thyShowName="true"
                                    ></thy-avatar>
                                }
                            } @else {
                                @let recordValues = record.values[field._id] | user: aiReferences()!;

                                <thy-avatar-list thyAvatarSize="xs">
                                    @for (item of recordValues; track $index) {
                                        <thy-avatar [thyName]="item.display_name!" [thySrc]="item.avatar!"></thy-avatar>
                                    }
                                </thy-avatar-list>
                            }
                        }
                        @case (AITableFieldType.createdBy) {
                            @let recordValues = record.values[field._id] | user: aiReferences()!;

                            @if (recordValues && recordValues.length) {
                                <thy-avatar
                                    [thyName]="recordValues[0].display_name!"
                                    [thySrc]="recordValues[0].avatar!"
                                    thySize="xs"
                                    thyShowName="true"
                                ></thy-avatar>
                            }
                        }
                        @case (AITableFieldType.updatedBy) {
                            @let recordValues = record.values[field._id] | user: aiReferences()!;

                            @if (recordValues && recordValues.length) {
                                <thy-avatar
                                    [thyName]="recordValues[0].display_name!"
                                    [thySrc]="recordValues[0].avatar!"
                                    thySize="xs"
                                    thyShowName="true"
                                ></thy-avatar>
                            }
                        }
                        @default {
                            <span class="text-truncate"> {{ record.values[field._id] }}</span>
                        }
                    }
                    <div class="autofill-container"></div>
                </div>
            }
            <div class="grid-column-blank"></div>
        </div>
    }
    <div class="grid-row-insert grid-row cursor-pointer" (click)="addRecord()">
        <thy-icon thyIconName="plus"></thy-icon>
    </div>
</div>

<div #activeBorder class="active-border"></div>
