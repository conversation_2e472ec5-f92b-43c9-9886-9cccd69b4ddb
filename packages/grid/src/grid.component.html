<div #container class="ai-table-grid-view">
    @if (hasContainerRect()) {
        <ai-table-renderer
            [config]="rendererConfig()"
            (koMousemove)="stageMousemove($event)"
            (koMousedown)="stageMousedown($event)"
            (koMouseup)="stageMouseup($event)"
            (koContextmenu)="stageContextmenu($event)"
            (koClick)="stageClick($event)"
            (koDblclick)="stageDblclick($event)"
            (koMouseleave)="stageMouseleave($event)"
            (koWheel)="stageWheel($event)"
        >
            @if (domToolTips().length > 0) {
                <div
                    class="ai-table-left-background-wrapper"
                    [style.height.px]="containerRect().height - fieldHeadHeight"
                    [style.top.px]="fieldHeadHeight"
                >
                    @for (domToolTip of domToolTips(); track trackBy(idx, domToolTip); let idx = $index) {
                        <div
                            class="ai-table-left-background"
                            [thyTooltip]="i18nTexts().rowAddFilterTooltip"
                            [style.--scroll-top.px]="domToolTip.top"
                        >
                            <thy-icon class="text-white" thyIconName="filter-line"></thy-icon>
                        </div>
                    }
                </div>
            }
            <div #horizontalBar class="ai-table-horizontal-scroll-bar-wrapper" [style.width.px]="containerRect().width">
                <div class="ai-table-scroll-bar-inner" [style.width.px]="scrollbarWidth()"></div>
            </div>
            <div
                #verticalBar
                class="ai-table-vertical-scroll-bar-wrapper"
                [style.height.px]="containerRect().height - fieldHeadHeight"
                [style.top.px]="fieldHeadHeight"
            >
                <div class="ai-table-scroll-bar-inner" [style.height.px]="scrollTotalHeight()"></div>
            </div>
        </ai-table-renderer>
    }
    <ai-table-drag [horizontalBar]="horizontalBarRef()" [verticalBar]="verticalBarRef()" (dragEnd)="dragEnd($event)"></ai-table-drag>
</div>
