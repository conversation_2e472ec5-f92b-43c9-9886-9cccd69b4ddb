// 自动生成的图标常量文件
export const apk = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/APK</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/APK" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="APK" transform="translate(2.000000, 0.000000)">
            <path d="M14.1176471,0 L2.74128887,0 C1.22731689,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.22731689,24 2.74128887,24 L17.2587111,24 C18.7726831,24 20,22.765548 20,21.2427744 L20,6 C20,6 14.1176471,0 14.1176471,0 Z" id="路径" fill="#78B354"></path>
            <path d="M12.4981525,12.5063279 L13.3031073,11.0370821 C13.3468152,10.9603305 13.3176766,10.8616498 13.2393665,10.8196191 C13.1628776,10.7775885 13.0645347,10.8049998 13.0226479,10.8835788 L12.2085874,12.367444 C11.5256507,12.0640922 10.7607616,11.8941421 9.95216453,11.8941421 C9.14356748,11.8941421 8.37867837,12.0640922 7.69574167,12.3656165 L6.88168113,10.8835788 C6.83979434,10.8049998 6.74145146,10.7775885 6.66496255,10.8196191 C6.58665247,10.8616498 6.55751384,10.9603305 6.60122179,11.0370821 L7.40617651,12.5063279 C5.8254057,13.3250121 4.75638212,14.881974 4.75638212,16.6673635 L15.1461281,16.6673635 C15.1479469,14.8801466 14.0789234,13.3250121 12.4981525,12.5063279 Z M7.58829297,14.7832933 C7.34789925,14.7832933 7.15303464,14.5877594 7.15303464,14.3465399 C7.15303464,14.1053205 7.34789925,13.9097865 7.58829297,13.9097865 C7.82868668,13.9097865 8.02355129,14.1053205 8.02355129,14.3465399 C8.02173013,14.5877594 7.82686552,14.7832933 7.58829297,14.7832933 Z M12.3178573,14.7832933 C12.0774635,14.7832933 11.8825989,14.5877594 11.8825989,14.3465399 C11.8825989,14.1053205 12.0774635,13.9097865 12.3178573,13.9097865 C12.558251,13.9097865 12.7531156,14.1053205 12.7531156,14.3465399 C12.7512944,14.5877594 12.5564298,14.7832933 12.3178573,14.7832933 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
            <path d="M14.1176471,0 L20,6 L15.3176471,6 C14.6549054,6 14.1176471,5.4627417 14.1176471,4.8 L14.1176471,0 L14.1176471,0 Z" id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const bak = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/BAK </title>
    <g id="1.Base基础/1.icon图标/12.文件类型/BAK-" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="BAK" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#E75A71"></path>
            <path d="M5.75267634,16.8 C6.25452726,16.8 6.64412206,16.6941176 6.92146073,16.4823529 C7.24502251,16.2252101 7.41010505,15.8319328 7.41010505,15.287395 C7.41010505,14.9243697 7.33086543,14.6294118 7.17238619,14.410084 C7.01390695,14.1907563 6.77618809,14.0470588 6.46583292,13.9789916 C6.70355178,13.8806723 6.88184092,13.7294118 7.01390695,13.5327731 C7.14597299,13.3210084 7.212006,13.0638655 7.212006,12.7613445 C7.212006,12.3453782 7.08654327,12.0201681 6.83561781,11.7781513 C6.57148574,11.5210084 6.21490745,11.4 5.75267634,11.4 L3.6,11.4 L3.6,16.8 L5.75267634,16.8 Z M5.56118059,13.6915966 L4.31975988,13.6915966 L4.31975988,12.0806723 L5.57438719,12.0806723 C5.89134567,12.0806723 6.12906453,12.1411765 6.27433717,12.2697479 C6.4196098,12.3907563 6.49224612,12.587395 6.49224612,12.8596639 C6.49224612,13.1470588 6.4196098,13.3588235 6.27433717,13.494958 C6.12906453,13.6235294 5.89134567,13.6915966 5.56118059,13.6915966 Z M5.65362681,16.1193277 L4.31975988,16.1193277 L4.31975988,14.3647059 L5.67343672,14.3647059 C6.0168084,14.3647059 6.27433717,14.4327731 6.43941971,14.5764706 C6.60450225,14.7201681 6.69034517,14.9546218 6.69034517,15.2722689 C6.69034517,15.5823529 6.57808904,15.8092437 6.36678339,15.9529412 C6.19509755,16.0588235 5.95737869,16.1193277 5.65362681,16.1193277 Z M8.44022011,16.8 L8.88264132,15.4084034 L10.9098549,15.4084034 L11.3522761,16.8 L12.1248624,16.8 L10.3089545,11.4 L9.49014507,11.4 L7.67423712,16.8 L8.44022011,16.8 Z M10.691946,14.7277311 L9.10055028,14.7277311 L9.88634317,12.2848739 L9.91275638,12.2848739 L10.691946,14.7277311 Z M13.3068534,16.8 L13.3068534,14.9319328 L13.9209605,14.2588235 L15.835918,16.8 L16.8,16.8 L14.4096048,13.7218487 L16.4962481,11.4 L15.5849925,11.4 L13.3068534,14.0092437 L13.3068534,11.4 L12.5870935,11.4 L12.5870935,16.8 L13.3068534,16.8 Z" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const bat = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/BAT</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/BAT" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="bat" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#378FE8"></path>
            <path d="M5.87562136,16.8 C6.40613432,16.8 6.8179799,16.6941176 7.11115812,16.4823529 C7.45319937,16.2252101 7.62771021,15.8319328 7.62771021,15.287395 C7.62771021,14.9243697 7.543945,14.6294118 7.3764146,14.410084 C7.20888419,14.1907563 6.95758858,14.0470588 6.6295082,13.9789916 C6.88080381,13.8806723 7.06927552,13.7294118 7.20888419,13.5327731 C7.34849286,13.3210084 7.4182972,13.0638655 7.4182972,12.7613445 C7.4182972,12.3453782 7.28566896,12.0201681 7.02041248,11.7781513 C6.74119513,11.5210084 6.36425172,11.4 5.87562136,11.4 L3.6,11.4 L3.6,16.8 L5.87562136,16.8 Z M5.67318879,13.6915966 L4.36086727,13.6915966 L4.36086727,12.0806723 L5.68714966,12.0806723 C6.02221047,12.0806723 6.27350608,12.1411765 6.42707562,12.2697479 C6.58064516,12.3907563 6.65742993,12.587395 6.65742993,12.8596639 C6.65742993,13.1470588 6.58064516,13.3588235 6.42707562,13.494958 C6.27350608,13.6235294 6.02221047,13.6915966 5.67318879,13.6915966 Z M5.77091486,16.1193277 L4.36086727,16.1193277 L4.36086727,14.3647059 L5.79185616,14.3647059 C6.15483871,14.3647059 6.42707562,14.4327731 6.60158646,14.5764706 C6.7760973,14.7201681 6.86684294,14.9546218 6.86684294,15.2722689 C6.86684294,15.5823529 6.74817557,15.8092437 6.52480169,15.9529412 C6.34331042,16.0588235 6.09201481,16.1193277 5.77091486,16.1193277 Z M8.71665785,16.8 L9.18434691,15.4084034 L11.32734,15.4084034 L11.7950291,16.8 L12.6117398,16.8 L10.6921206,11.4 L9.8265468,11.4 L7.90692755,16.8 L8.71665785,16.8 Z M11.0969857,14.7277311 L9.41470122,14.7277311 L10.2453728,12.2848739 L10.2732946,12.2848739 L11.0969857,14.7277311 Z M15.1386568,16.8 L15.1386568,12.1033613 L16.8,12.1033613 L16.8,11.4 L12.7164463,11.4 L12.7164463,12.1033613 L14.38477,12.1033613 L14.38477,16.8 L15.1386568,16.8 Z" id="BAT" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const cs = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/C#</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/C#" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="c#" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#78B354"></path>
            <path d="M9.92568659,18 C10.3457189,17.993617 10.7592892,17.9425532 11.1663974,17.8468085 C11.4894992,17.7702128 11.7996769,17.6585106 12.0969305,17.5117021 L11.6122779,16.037234 C11.3731826,16.1585106 11.1211632,16.2446809 10.8562197,16.2957447 C10.5912763,16.3468085 10.3231018,16.3723404 10.0516963,16.3723404 C9.76736672,16.3723404 9.49596123,16.3117021 9.23747981,16.1904255 C8.96607431,16.056383 8.73021002,15.8776596 8.52988691,15.6542553 C8.12924071,15.1819149 7.93214863,14.6361702 7.93861066,14.0170213 C7.93861066,13.7234043 7.97415186,13.4329787 8.04523425,13.1457447 C8.12924071,12.8329787 8.27786753,12.5489362 8.4911147,12.293617 C8.6914378,12.0765957 8.93053312,11.9106383 9.20840065,11.7957447 C9.49919225,11.693617 9.80290792,11.6393617 10.1195477,11.6329787 C10.6558966,11.6265957 11.1793215,11.7191489 11.6898223,11.9106383 L11.7576737,10.3882979 C11.1567044,10.1393617 10.5298869,10.0117021 9.87722132,10.0053191 C9.57996769,10.0117021 9.28917609,10.0404255 9.00484653,10.0914894 C8.73344103,10.1489362 8.47172859,10.2287234 8.21970921,10.3308511 C7.72859451,10.5414894 7.30533118,10.8446809 6.94991922,11.2404255 C6.59450727,11.6617021 6.33925687,12.1340426 6.18416801,12.6574468 C6.06785137,13.1042553 6.00646204,13.5574468 6,14.0170213 C6,14.4893617 6.06138934,14.9489362 6.18416801,15.3957447 C6.26817447,15.6638298 6.37802908,15.9223404 6.51373183,16.1712766 C6.64297254,16.4010638 6.79483037,16.6148936 6.96930533,16.812766 C7.33117932,17.1957447 7.76090468,17.4893617 8.25848142,17.693617 C8.79483037,17.8978723 9.35056543,18 9.92568659,18 Z M13.1728595,12.4659574 L13.3231018,11.3601064 L14.0306947,11.3601064 L13.8707593,12.4659574 L14.1470113,12.4659574 L14.3069467,11.3601064 L14.8594507,11.3601064 L14.8594507,11.1111702 L14.3408724,11.1111702 L14.447496,10.3547872 L15,10.3547872 L15,10.1058511 L14.4814216,10.1058511 L14.641357,9 L14.365105,9 L14.2051696,10.1058511 L13.4975767,10.1058511 L13.6672052,9 L13.3909532,9 L13.2310178,10.1058511 L12.6785137,10.1058511 L12.6785137,10.3547872 L13.1970921,10.3547872 L13.0904685,11.1111702 L12.5379645,11.1111702 L12.5379645,11.3601064 L13.0565428,11.3601064 L12.8966074,12.4659574 L13.1728595,12.4659574 Z M14.0646204,11.1111702 L13.3570275,11.1111702 L13.4636511,10.3547872 L14.1712439,10.3547872 L14.0646204,11.1111702 Z" id="形状结合" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const css = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/CSS</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/CSS" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="css" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#53ADEF"></path>
            <polygon id="路径" fill="#FFFFFF" points="13.6581388 11.660821 10.231646 13.1830211 10.2234617 13.1863691 13.5370112 13.1863691 13.1578054 17.7139099 10.2371022 18.6 10.231646 18.598326 10.231646 18.6 7.29511973 17.6971702 7.10524401 15.41666 8.56368596 15.41666 8.6580782 16.5979275 10.2185511 17.007493 10.231646 17.0035871 10.231646 17.0052611 11.8472264 16.5326425 11.9579873 14.6298924 10.231646 14.6243125 7.01085177 14.6131527 6.90009093 13.1858111 10.231646 11.7445197 10.4258866 11.660821 6.77405273 11.660821 6.6 10.2 13.8 10.2 13.6581388 11.6602631"></polygon>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const csv = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/CSV</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/CSV" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="csv" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#14BC64"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
            <path d="M5.86532461,16.8 C6.37684952,16.8 6.81529945,16.6398922 7.18067438,16.3196765 C7.57262204,15.977628 7.81841973,15.5045822 7.92471062,14.9005391 L7.22053347,14.9005391 C7.12752894,15.3080863 6.96144942,15.6137466 6.72229492,15.8175202 C6.49642677,16.0067385 6.20412682,16.1013477 5.85868143,16.1013477 C5.32722698,16.1013477 4.93527932,15.919407 4.68283845,15.5555256 C4.44368395,15.2134771 4.32410669,14.7331536 4.32410669,14.1072776 C4.32410669,13.5032345 4.44368395,13.0229111 4.68948163,12.6735849 C4.94856568,12.2878706 5.33387016,12.0986523 5.84539507,12.0986523 C6.19084046,12.0986523 6.46985405,12.1787062 6.69572219,12.3460916 C6.92159034,12.5134771 7.07438349,12.7754717 7.15410166,13.1247978 L7.85827881,13.1247978 C7.78520382,12.6008086 7.57262204,12.1787062 7.22717665,11.8730458 C6.86844489,11.5528302 6.41006543,11.4 5.85868143,11.4 C5.10135883,11.4 4.53004529,11.6692722 4.13145445,12.222372 C3.7727227,12.709973 3.6,13.3358491 3.6,14.1072776 C3.6,14.8932615 3.76607952,15.5191375 4.11152491,15.9849057 C4.50347257,16.5234501 5.08807247,16.8 5.86532461,16.8 Z M10.3893306,16.8 C10.9872169,16.8 11.4522396,16.6690027 11.7843986,16.4070081 C12.1165576,16.1377358 12.2826371,15.7738544 12.2826371,15.3080863 C12.2826371,14.8277628 12.0766985,14.4566038 11.6714645,14.1873315 C11.4854555,14.0636119 11.0669351,13.8962264 10.4291897,13.6778976 C9.98409663,13.532345 9.71172622,13.4231806 9.60543533,13.3576819 C9.36628083,13.219407 9.25334675,13.0374663 9.25334675,12.8045822 C9.25334675,12.5425876 9.35299446,12.3533693 9.55893306,12.2369272 C9.72501258,12.1350404 9.96416709,12.084097 10.2763966,12.084097 C10.6351283,12.084097 10.9008556,12.1568733 11.0868646,12.3097035 C11.2728737,12.4552561 11.3924509,12.7026954 11.4588827,13.0374663 L12.1763463,13.0374663 C12.129844,12.4698113 11.9438349,12.0549865 11.6116759,11.7857143 C11.2994464,11.5237197 10.8676397,11.4 10.3162557,11.4 C9.81137393,11.4 9.39285355,11.5237197 9.06069451,11.771159 C8.70860594,12.0331536 8.53588324,12.3897574 8.53588324,12.848248 C8.53588324,13.2994609 8.71524912,13.6487871 9.08062406,13.8889488 C9.22677403,13.9762803 9.58550579,14.1145553 10.1701057,14.3183288 C10.694917,14.4929919 11.0071465,14.609434 11.1134373,14.667655 C11.4123805,14.8350404 11.5651736,15.0606469 11.5651736,15.351752 C11.5651736,15.5846361 11.4588827,15.7665768 11.246301,15.8975741 C11.0337192,16.0285714 10.7480624,16.1013477 10.3893306,16.1013477 C9.99073981,16.1013477 9.69843986,16.0212938 9.50578762,15.861186 C9.29320584,15.6865229 9.16034222,15.3881402 9.10719678,14.980593 L8.38973327,14.980593 C8.42959235,15.6355795 8.64217413,16.115903 9.03412179,16.4215633 C9.35963764,16.6690027 9.81137393,16.8 10.3893306,16.8 Z M15.0993457,16.6981132 L16.8,11.5018868 L16.0161047,11.5018868 L14.6741822,15.8102426 L14.6542526,15.8102426 L13.305687,11.5018868 L12.5217916,11.5018868 L14.2224459,16.6981132 L15.0993457,16.6981132 Z" id="CSV" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>`;

export const defaultFile = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/文件</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/文件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="pdf" transform="translate(2.000000, 0.000000)">
            <path d="M14.1176471,0 L2.74128887,0 C1.22731689,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.22731689,24 2.74128887,24 L17.2587111,24 C18.7726831,24 20,22.765548 20,21.2427744 L20,6 C20,6 14.1176471,0 14.1176471,0 Z" id="路径" fill="#378FE8"></path>
            <path d="M14.1176471,0 L20,6 L15.3176471,6 C14.6549054,6 14.1176471,5.4627417 14.1176471,4.8 L14.1176471,0 L14.1176471,0 Z" id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0.400000006"></path>
            <path d="M14.5880967,13.1173301 C14.8152676,13.1173301 15,13.3453703 15,13.6274691 L15,13.6274691 L14.0557428,17.489861 C14.0557428,17.7719598 13.8716345,18 13.6438394,18 L13.6438394,18 L5.9674589,18 C5.74028797,18 5.55555556,17.7719598 5.55555556,17.489861 L5.55555556,17.489861 L6.49981277,13.6274691 C6.49981277,13.3453703 6.68454518,13.1173301 6.91171612,13.1173301 L6.91171612,13.1173301 Z M8.54763993,10 C8.74912861,10 9.00458747,10.1412782 9.17909105,10.4590101 C9.3284104,10.7759998 9.5345565,10.9507235 9.79752937,10.9831811 L9.79752937,10.9831811 L9.89693802,10.9920475 C10.2885983,11.0188347 11.410488,11.0333709 13.2626071,11.0356559 C13.712505,11.0318676 13.8888889,11.3623841 13.8888889,11.6829992 L13.8888889,11.6829992 L13.8888889,12.2278307 L6.32346878,12.2278307 C6.07820429,12.2278307 5.88031363,12.4654087 5.88031363,12.7566149 L5.88031363,12.7566149 L5,15.9677076 L5,15.9677076 L5,10.8633729 C5,10.0518557 5.7639779,10 5.96546658,10 Z" id="形状结合" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>`;

export const doc = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/DOC</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/DOC" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="word" transform="translate(1.800000, 0.000000)">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#378FE8"></path>
            <polygon id="W" fill="#FFFFFF" fill-rule="nonzero" points="8.75949099 18 10.1847296 12.5848739 10.2254507 12.5848739 11.640509 18 12.9028632 18 15 10.8 13.6562036 10.8 12.2920467 16.2756303 12.2513256 16.2756303 10.8159067 10.8 9.58409332 10.8 8.14867444 16.2756303 8.10795334 16.2756303 6.74379639 10.8 5.4 10.8 7.48695652 18"></polygon>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const exe = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/EXE</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/EXE" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="EXE" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z M8.15340312,5.27127736 L6.60000001,5.27127736 L6.60000001,6.80307537 L8.15340312,6.80307537 L8.15340312,8.33487339 L6.60000001,8.33487339 L6.60000001,9.8666714 L8.15340312,9.8666714 L8.15340312,11.3984694 L6.59997259,11.3984694 L6.59997259,9.8666714 L5.04656949,9.8666714 L5.04656949,8.33487339 L6.59997259,8.33487339 L6.59997259,6.80307537 L5.04656949,6.80307537 L5.04656949,5.27127736 L6.59997259,5.27127736 L6.59997259,3.73947935 L5.04656949,3.73947935 L5.04656949,2.20768134 L6.60000001,2.20768134 L6.60000001,3.73947935 L8.15340312,3.73947935 L8.15340312,5.27127736 Z" id="形状" fill="#667580"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
            <path d="M8.181848,19.8 L8.181848,19.3310924 L5.97211591,19.3310924 L5.97211591,18.1865546 L7.96664844,18.1865546 L7.96664844,17.7176471 L5.97211591,17.7176471 L5.97211591,16.6689076 L8.09261892,16.6689076 L8.09261892,16.2 L5.4,16.2 L5.4,19.8 L8.181848,19.8 Z M9.1213778,19.8 L10.1238928,18.3731092 L11.1264079,19.8 L11.8244943,19.8 L10.4650629,17.9394958 L11.7352652,16.2 L11.0371788,16.2 L10.1238928,17.5008403 L9.21060689,16.2 L8.5125205,16.2 L9.77222526,17.9394958 L8.42329142,19.8 L9.1213778,19.8 Z M15,19.8 L15,19.3310924 L12.7902679,19.3310924 L12.7902679,18.1865546 L14.7848004,18.1865546 L14.7848004,17.7176471 L12.7902679,17.7176471 L12.7902679,16.6689076 L14.9107709,16.6689076 L14.9107709,16.2 L12.218152,16.2 L12.218152,19.8 L15,19.8 Z" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>`;

export const fla = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/FLA</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/FLA" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="FLA" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#F26225"></path>
            <path d="M4.41013514,16.8 L4.41013514,14.3798319 L7.04864865,14.3798319 L7.04864865,13.6764706 L4.41013514,13.6764706 L4.41013514,12.1033613 L7.20472973,12.1033613 L7.20472973,11.4 L3.6,11.4 L3.6,16.8 L4.41013514,16.8 Z M11.6418919,16.8 L11.6418919,16.0966387 L8.72094595,16.0966387 L8.72094595,11.4 L7.91824324,11.4 L7.91824324,16.8 L11.6418919,16.8 Z M12.6527027,16.8 L13.1506757,15.4084034 L15.4324324,15.4084034 L15.9304054,16.8 L16.8,16.8 L14.7560811,11.4 L13.8344595,11.4 L11.7905405,16.8 L12.6527027,16.8 Z M15.1871622,14.7277311 L13.3959459,14.7277311 L14.2804054,12.2848739 L14.3101351,12.2848739 L15.1871622,14.7277311 Z" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const html = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/HTML</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/HTML" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="HTML" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#F26225"></path>
            <polygon id="路径" fill="#FFFFFF" points="13.72 11.6913838 8.24 11.6913838 8.34 13.1169713 13.54 13.1169713 13.18 17.7446475 10.16 18.6 7.24 17.7446475 7.04 15.3759791 8.5 15.3759791 8.6 16.5164491 10.16 16.9770235 11.8 16.5164491 11.9 14.6302872 6.96 14.6302872 6.6 10.2 13.8 10.2 13.72 11.6913838"></polygon>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const img = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/图片</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/图片" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="excel" transform="translate(1.800000, 0.000000)">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#53ADEF"></path>
            <path d="M13.824,10.32 C14.4072127,10.32 14.88,10.7927873 14.88,11.376 L14.88,16.344 C14.88,16.9272127 14.4072127,17.4 13.824,17.4 L6.696,17.4 C6.1127873,17.4 5.64,16.9272127 5.64,16.344 L5.64,11.376 C5.64,10.7927873 6.1127873,10.32 6.696,10.32 L13.824,10.32 Z M11.32246,13.5878637 L9.69467725,15.2453409 C9.55321852,15.4095412 9.31061521,15.4382669 9.13500743,15.3157947 L9.11341755,15.299697 L8.30168,14.6534455 L6.34,16.0480344 L6.34,16.7548593 C6.34,16.7645794 6.34633183,16.7728232 6.35511699,16.7757318 L6.36210526,16.7768528 L14.1578947,16.7768528 C14.1676669,16.7768528 14.175951,16.7705551 14.1788737,16.7618135 L14.18,16.7548593 L14.18,16.0816037 L11.32246,13.5878637 Z M8.24842105,11.5809568 C7.70719972,11.5809568 7.26842105,12.0175157 7.26842105,12.5559989 C7.26842105,13.0944822 7.70719972,13.5310411 8.24842105,13.5310411 C8.78964239,13.5310411 9.22842105,13.0944822 9.22842105,12.5559989 C9.22842105,12.0175157 8.78964239,11.5809568 8.24842105,11.5809568 Z" id="形状结合" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const ipa = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/IPA</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/IPA" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="IPA" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#53ADEF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
            <path d="M5.43226366,16.8 L5.43226366,11.4 L4.8,11.4 L4.8,16.8 L5.43226366,16.8 Z M7.86244579,16.8 L7.86244579,14.5915966 L9.49384215,14.5915966 C10.7739809,14.5915966 11.421856,14.0546218 11.421856,12.9882353 C11.421856,11.9294118 10.7817866,11.4 9.50164788,11.4 L7.22237641,11.4 L7.22237641,16.8 L7.86244579,16.8 Z M9.46261925,14.0546218 L7.86244579,14.0546218 L7.86244579,11.9369748 L9.46261925,11.9369748 C9.90754553,11.9369748 10.2353859,12.0201681 10.4539462,12.2016807 C10.6725065,12.3605042 10.7817866,12.6252101 10.7817866,12.9882353 C10.7817866,13.3512605 10.6725065,13.6159664 10.461752,13.789916 C10.2431917,13.9638655 9.91535126,14.0546218 9.46261925,14.0546218 Z M12.3585429,16.8 L12.951778,15.287395 L15.5120555,15.287395 L16.1052905,16.8 L16.8,16.8 L14.6065915,11.4 L13.8650477,11.4 L11.6716392,16.8 L12.3585429,16.8 Z M15.3091067,14.7655462 L13.1547268,14.7655462 L14.224111,12.0655462 L14.2553339,12.0655462 L15.3091067,14.7655462 Z" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>`;

export const java = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/JAVA</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/JAVA" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="java" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#F59203"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
            <path d="M12.6345082,9.54776364 C12.3269346,9.75591328 12.0273145,9.93314034 11.6981721,10.1903736 C11.449332,10.3857816 11.0000782,10.6528974 10.9758547,11.0127809 C10.9383581,11.5600927 11.8159607,12.0662807 11.3504576,12.7602158 C11.1739354,13.0247912 10.8743257,13.1379515 10.4944135,13.2998766 C10.4489634,13.2201892 10.5932879,13.1532534 10.655008,13.0684554 C11.2382997,12.2562593 10.0478143,11.9865933 10.200113,10.9869589 C10.3471026,10.0207975 11.5322787,9.68386685 12.6345082,9.54776364 L12.6345082,9.54776364 Z M8.91639428,12.8883592 C8.64099764,13.0117208 8.18079344,13.0349924 7.98005813,13.2995579 C8.18344807,13.4461911 8.48041354,13.4407717 8.75581018,13.453842 C9.88457556,13.5026069 11.2970333,13.4101679 12.2599157,13.2482427 C12.2920926,13.3151885 12.1261993,13.4305704 12.0190292,13.5051671 C11.4118355,13.9316577 9.52324579,14.052469 8.22061282,13.967681 C7.78463208,13.9393087 6.78690772,13.8290175 6.77596776,13.4538321 C6.76269464,12.9989692 7.98503556,12.9502044 8.48803522,12.9141812 C8.58692001,12.9065302 8.77172757,12.8679567 8.91606245,12.8883592 L8.91639428,12.8883592 Z M8.35466103,14.044828 C8.48041354,14.0601299 8.26639474,14.1321664 8.3012367,14.2246154 C8.76144089,14.661626 10.192481,14.5382744 10.8958945,14.4043927 C11.0428736,14.3760205 11.190195,14.2912224 11.2970333,14.3014337 C11.5644661,14.3247053 11.7356686,14.6227436 11.9659367,14.6613072 C11.1526984,15.0135398 9.58264303,15.1805755 8.43495308,14.9695568 C8.13799798,14.9156813 7.62171482,14.7613972 7.60579743,14.5325362 C7.58157398,14.2214175 8.11676097,14.0904149 8.35466103,14.0441904 L8.35466103,14.044828 Z M8.70238585,15.0986466 C8.78533252,15.1244686 8.67285314,15.1681427 8.67550777,15.2016057 C8.9190489,15.6077137 10.1227971,15.4636307 10.7353,15.3300679 C10.8583979,15.3016956 10.9814958,15.2194579 11.0830249,15.2271088 C11.3932531,15.2475114 11.5110417,15.5586201 11.7784745,15.6124855 C10.925085,16.1135828 8.85732886,16.3166318 8.00660439,15.6896326 C7.96645318,15.2322095 8.34900958,15.1808943 8.70205402,15.0986466 L8.70238585,15.0986466 Z M7.9266338,15.8697288 C7.67247416,15.931564 7.01452111,15.8439068 6.99030802,16.1779684 C6.98234415,16.3064306 7.21493496,16.4581644 7.36491092,16.5120299 C8.2368724,16.8282392 9.98909106,16.877014 11.4307393,16.7176292 C12.099653,16.6430424 13.3568256,16.4272319 13.1962415,15.7667697 C13.3969768,15.7900413 13.5761432,15.9134029 13.5973802,16.1008313 C13.6776827,16.8100682 11.9815326,17.1081066 11.2970333,17.180153 C9.80693814,17.3369775 7.94521618,17.3060649 6.80317767,16.9232286 C6.4312294,16.799867 5.98197554,16.570996 6.00055791,16.2292935 C6.03274525,15.6536094 7.47705847,15.4942345 7.92664417,15.86941 L7.9266338,15.8697288 Z M10.2004449,18 C9.19742163,17.8944906 8.23156315,17.7532768 7.41832489,17.409024 C9.54481462,17.89991 12.6451267,17.8638968 14.1325672,16.818038 C14.2128697,16.7616222 14.2878524,16.6510122 14.4,16.6637638 C14.0253971,17.7430756 12.5863931,17.8176723 11.3504576,18 L10.200113,18 L10.2004449,18 Z M10.8958945,7.8 C11.0830249,7.96957611 11.2167309,8.28578544 11.2167309,8.62239731 C11.2167309,9.61692104 10.1201425,10.1926051 9.58496583,10.8581779 C9.46716683,11.0073615 9.31487844,11.2359136 9.31753306,11.4749759 C9.32284231,12.0171871 9.90614439,12.6237938 10.1201425,13.0684554 C9.74554992,12.8319434 9.29098681,12.5160529 8.96980825,12.1434178 C8.64896151,11.773343 8.32778295,11.1721557 8.62208343,10.6528974 C9.06337342,9.87161396 10.3769567,9.40654984 10.8424702,8.57140095 C10.9549495,8.36835193 11.0432055,8.05755206 10.8958945,7.80031879 L10.8958945,7.8 Z M12.4742559,13.1456024 C13.0253603,12.6932799 13.95871,12.870507 13.9988612,13.6339482 C14.0443113,14.5283819 13.0250285,15.0294693 12.2867834,15.0731434 C12.629199,14.7594745 13.5333478,14.2558468 13.3568256,13.5312979 C13.2844974,13.2383602 12.9045956,13.0611232 12.4742456,13.1459212 L12.4742559,13.1456024 Z" id="形状" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>`;

export const js = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/JS</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/JS" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="js" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#FBC100"></path>
            <path d="M9.16868571,9.76744186 L7.45714286,9.76744186 L7.45714286,14.1104651 C7.45714286,15.1831395 7.07314286,15.4866279 6.3984,15.4866279 C6.08571429,15.4866279 5.80045714,15.4343023 5.58102857,15.3610465 L5.4,16.6325581 C5.71268571,16.7372093 6.20091429,16.8 6.57394286,16.8 C8.11542857,16.8 9.16868571,16.1093023 9.16868571,14.1209302 L9.16868571,9.76744186 Z M12.9099429,9.6 C11.2477714,9.6 10.2,10.5 10.2,11.6877907 C10.2,12.7133721 11.0064,13.3517442 12.1858286,13.7651163 C13.0361143,14.052907 13.3707429,14.3145349 13.3707429,14.7383721 C13.3707429,15.2040698 12.9812571,15.5023256 12.2461714,15.5023256 C11.5659429,15.5023256 10.9460571,15.2825581 10.5236571,15.0680233 L10.2,16.3604651 C10.6004571,16.5802326 11.3465143,16.7947674 12.1529143,16.7947674 C14.0948571,16.7947674 15,15.8005814 15,14.6337209 C15,13.6395349 14.424,13.0011628 13.1732571,12.5459302 C12.2461714,12.1953488 11.8621714,11.9965116 11.8621714,11.5517442 C11.8621714,11.2011628 12.1968,10.8819767 12.8934857,10.8819767 C13.5737143,10.8819767 14.0454857,11.1017442 14.3197714,11.2377907 L14.7312,9.92965116 C14.2978286,9.75174419 13.7163429,9.6 12.9099429,9.6 L12.9099429,9.6 Z" id="形状" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const key = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/KEY</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/KEY" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="keynote" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#378FE8"></path>
            <path d="M14.9911156,14.5898439 L14.9911156,15.1248048 L10.4736066,15.1248048 L10.4736066,17.8869141 L11.6598352,17.8869141 C11.8614793,17.8869141 12.024944,18.0465438 12.024944,18.243457 C12.024944,18.4403703 11.8614793,18.6 11.6598352,18.6 L8.74016477,18.6 C8.53852074,18.6 8.37505596,18.4403703 8.37505596,18.243457 C8.37505596,18.0465438 8.53852074,17.8869141 8.74016477,17.8869141 L9.9263934,17.8869141 L9.9263934,15.1253907 L5.40948438,15.1253907 L5.40948438,14.5904298 L14.9911156,14.5904298 L14.9911156,14.5898439 Z M13.6638836,9.60000011 C13.9224898,9.60000011 14.1462952,9.77695335 14.1996965,10.024219 L14.9881156,13.6775392 C15.0223332,13.8352785 14.9817865,13.9996722 14.8777768,14.1248996 C14.7737671,14.2501269 14.6174161,14.3227976 14.4523026,14.3226566 L5.94769737,14.3226566 C5.78258395,14.3227976 5.62623288,14.2501269 5.5222232,14.1248996 C5.41821351,13.9996722 5.37766679,13.8352785 5.41188445,13.6775392 L6.20030346,10.024219 C6.25366803,9.77684681 6.47724125,9.59983737 6.73611641,9.60000011 L13.6638836,9.60000011 L13.6638836,9.60000011 Z M12.9612666,10.5662111 L12.9120655,10.5703127 L11.9358419,10.7841799 C11.8708856,10.7984107 11.819027,10.8460683 11.8005407,10.9085205 C11.7820544,10.9709728 11.7998766,11.0382999 11.8470397,11.0841799 L12.0114437,11.2441408 L11.317227,11.9226564 C11.2323411,12.04108 11.0938739,12.1118994 10.9458497,12.1125982 C10.7978254,12.1132971 10.6586634,12.0437885 10.572609,11.926172 L10.3470035,11.6718752 L10.1921998,11.4996096 C10.0024563,11.253348 9.70373817,11.1103231 9.3881804,11.1146486 C9.11037369,11.1146486 8.85956764,11.2248049 8.67836328,11.401758 L8.66576298,11.4140627 L7.53653571,12.536133 C7.41893287,12.6708986 7.41893287,12.7957033 7.53653571,12.9146486 C7.65353854,13.0318361 7.78194165,13.0318361 7.92354507,12.9146486 L9.02457163,11.8382814 C9.10958742,11.7201665 9.24796462,11.6496296 9.39580529,11.6490475 C9.54364595,11.6484653 9.68260115,11.7179102 9.76858958,11.8353517 L9.99419502,12.0896486 L10.1483988,12.2619142 C10.3380033,12.508008 10.6374105,12.6515626 10.9524182,12.6468752 C11.2302249,12.6468752 11.4810309,12.5367189 11.6622353,12.3597658 L11.6754356,12.3474611 L12.4014531,11.6255861 L12.603658,11.8230471 C12.6506402,11.8691037 12.7195849,11.8865078 12.7835375,11.8684552 C12.8474902,11.8504026 12.8962927,11.7997607 12.9108654,11.7363283 L13.1298707,10.783008 C13.1434944,10.7234958 13.1249957,10.6613137 13.0808396,10.6181935 C13.0366834,10.5750733 12.9730075,10.5570086 12.9120655,10.5703127 L12.9612666,10.5662111 L12.9612666,10.5662111 Z" id="形状" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const mp3 = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/MP3</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/MP3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="MP3" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#14BC64"></path>
            <path d="M14.3998147,10.9584359 L14.3998147,9.80061073 C14.3998147,9.73573225 14.365358,9.67500695 14.3073452,9.63722297 C14.2490817,9.59963677 14.1742711,9.58982737 14.1071891,9.61093933 L8.22903883,11.4501715 C8.13751771,11.4786009 8.0773139,11.5575705 8.07973383,11.6451233 L8.1083369,12.6102117 L8.1083369,16.7193896 C7.77229436,16.679529 7.3953856,16.7339158 7.05961556,16.872434 C6.66888489,17.0340022 6.35183113,17.2932788 6.1673609,17.602789 C5.97248064,17.9307116 5.94675531,18.2751183 6.0956352,18.5727029 C6.21928028,18.8205583 6.44682953,19.0064721 6.75383298,19.1109441 C6.92647609,19.1700576 7.11490322,19.2 7.31322806,19.2 C7.58703929,19.2 7.87219801,19.1414897 8.1392836,19.0314406 C8.64383795,18.8232282 9.02443109,18.4467135 9.15185867,18.0358462 C9.19777184,17.9667057 9.19777184,17.871519 9.19777184,17.8186649 L9.19777184,17.7787055 C9.1988837,17.7512154 9.1988837,17.7185735 9.19769554,17.6866831 L9.19570074,17.6866831 C9.19801166,17.5156317 9.19801166,16.7122797 9.19801166,12.6933345 L13.2973972,11.4398973 L13.2973972,14.9378371 C12.9612674,14.8979864 12.5845331,14.9523633 12.2486758,15.09101 C11.4194046,15.4335972 10.9863689,16.19608 11.2846846,16.7913482 C11.4775264,17.1770988 11.9453132,17.4165489 12.5052424,17.4165489 C12.7794024,17.4165489 13.0638635,17.359067 13.3275045,17.2500858 C13.8620136,17.0294634 14.2504552,16.629068 14.3574551,16.1951307 C14.3868103,16.1330507 14.3868103,16.0577794 14.3868103,16.007971 C14.388162,15.9688422 14.3878677,15.936576 14.3867449,15.9047944 L14.3806188,15.90518 C14.3870937,15.6899567 14.3870937,14.8190761 14.3870937,11.0271907 C14.3952801,11.0055349 14.4,10.9825243 14.4,10.9585744 L14.3998147,10.9584359 Z" id="路径" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const page = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/文档</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/文档" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="pages" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#9466CD"></path>
            <path d="M14.0506888,10.580838 L13.1550425,11.3323745 L12.1459551,10.1297794 L13.0412113,9.37820876 C13.3451781,9.12313077 13.6002437,9.42711776 13.6002437,9.42711776 L14.0995997,10.0217845 C14.0995987,10.0217951 14.3546653,10.3257715 14.0506888,10.580838 Z M5.66861701,16.5899183 L7.68476284,15.9221129 L6.67171032,14.7148039 L5.66861701,16.5899183 Z M7.97860307,15.6762747 L12.8292113,11.6061311 L11.8198728,10.4032484 L6.96925313,14.4734016 L7.97860307,15.6762747 Z M5.7,17.4 L14.7,17.4 C14.8656854,17.4 15,17.5343146 15,17.7 C15,17.8656854 14.8656854,18 14.7,18 L5.7,18 C5.53431458,18 5.4,17.8656854 5.4,17.7 C5.4,17.5343146 5.53431458,17.4 5.7,17.4 Z" id="形状结合" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const pdf = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/PDF</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/PDF" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="pdf" transform="translate(2.000000, 0.000000)">
            <path d="M14.1176471,0 L2.74128887,0 C1.22731689,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.22731689,24 2.74128887,24 L17.2587111,24 C18.7726831,24 20,22.765548 20,21.2427744 L20,6 C20,6 14.1176471,0 14.1176471,0 Z" id="形状" fill="#DD2A42"></path>
            <path d="M13.465286,16.1960788 C12.7331102,16.140608 12.0280521,15.863254 11.458582,15.3640169 C10.3467596,15.6136354 9.28917236,15.9741956 8.23158516,16.417962 C7.39093891,17.9434089 6.60452791,18.72 5.92658739,18.72 C5.79099929,18.72 5.62829356,18.6922646 5.51982309,18.6090584 C5.22152926,18.4703814 5.05882353,18.165292 5.05882353,17.8602027 C5.05882353,17.6105841 5.11305877,16.9171991 7.68923274,15.7800478 C8.2858204,14.6706319 8.74681995,13.5334806 9.12646664,12.3408585 C8.8010552,11.675209 8.09599706,10.0388205 8.58411423,9.20675855 C8.74681995,8.90166918 9.0722314,8.73525679 9.42476047,8.76299218 C9.69593668,8.76299218 9.96711289,8.90166917 10.1298186,9.12355235 C10.4823477,9.62278951 10.4552301,10.6767346 9.99423051,12.2299169 C10.4281124,13.0619788 10.9975825,13.8108346 11.675523,14.4487487 C12.244993,14.3378071 12.8144631,14.254601 13.3839331,14.2546009 C14.6584613,14.2823363 14.8482846,14.8925151 14.821167,15.2530753 C14.821167,16.1960788 13.9262855,16.1960788 13.465286,16.1960788 L13.465286,16.1960788 Z M5.76470588,17.76 L5.85294118,17.7351724 C6.26470588,17.6110345 6.5882353,17.3627586 6.82352941,17.04 C6.38235294,17.1889655 6.02941176,17.4372414 5.76470588,17.76 Z M9.42619971,9.48 L9.34098347,9.48 C9.31257806,9.48 9.25576723,9.48 9.22736181,9.51 C9.11374016,10.02 9.1989564,10.56 9.3977943,11.04 C9.56822678,10.53 9.56822678,9.98999999 9.42619971,9.48 L9.42619971,9.48 Z M9.66013073,13.56 L9.63398693,13.6148571 L9.60784314,13.5874286 C9.37254903,14.2182857 9.11111112,14.8491429 8.82352941,15.4525714 L8.87581699,15.4251429 L8.87581699,15.48 C9.4509804,15.2605714 10.0784314,15.0685714 10.6535948,14.9314286 L10.627451,14.904 L10.7058824,14.904 C10.3137255,14.4925714 9.94771242,14.0262857 9.66013073,13.56 L9.66013073,13.56 Z M13.4964706,15.12 C13.2635294,15.12 13.0564706,15.12 12.8235294,15.189282 C13.0823529,15.3624871 13.3411765,15.4317692 13.6,15.4664102 C13.7811765,15.5010512 13.9623529,15.4664102 14.1176471,15.3971281 C14.1176471,15.2932051 14.0141176,15.12 13.4964706,15.12 Z" id="形状备份" fill="#FFFFFF" fill-rule="nonzero"></path>
            <path d="M14.1176471,0 L20,6 L15.3176471,6 C14.6549054,6 14.1176471,5.4627417 14.1176471,4.8 L14.1176471,0 L14.1176471,0 Z" id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const php = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/PHP</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/PHP" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="php" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#AC8FD7"></path>
            <path d="M7.16456528,13.4691011 C6.93145216,14.7205056 6.10817921,14.5907303 5.09605513,14.5907303 L5.5003146,12.4092697 C6.62161825,12.4092697 7.38292442,12.2825843 7.16456528,13.4691011 Z M3.6,16.8 L4.68294326,16.8 L4.93966278,15.4157303 C6.1524412,15.4157303 6.90489496,15.508427 7.60128355,14.8255618 C8.37144211,14.0839888 8.57209644,12.7646067 8.02324781,12.1033708 C7.7370203,11.7573034 7.27669565,11.5873596 6.65112624,11.5873596 L4.5649113,11.5873596 L3.6,16.8 Z M9.07963388,10.2 L10.1566755,10.2 L9.89995603,11.5842697 C10.8294577,11.5842697 11.6910911,11.5132022 12.1071537,11.9148876 C12.543872,12.3351124 12.3343653,12.8727528 11.8622374,15.4095506 L10.7704418,15.4095506 C11.2248648,12.9561798 11.310438,12.7522472 11.1451932,12.5668539 C10.9858501,12.3876404 10.6229018,12.4247191 9.74651448,12.4247191 L9.19176425,15.4095506 L8.11472259,15.4095506 L9.07963388,10.2 L9.07963388,10.2 Z M15.627457,13.4691011 C15.3913931,14.7390449 14.5445138,14.5907303 13.5589469,14.5907303 L13.9632064,12.4092697 C15.0904116,12.4092697 15.8458162,12.2825843 15.627457,13.4691011 L15.627457,13.4691011 Z M12.0628917,16.8 L13.1487858,16.8 L13.4055053,15.4157303 C14.6802505,15.4157303 15.3854915,15.4929775 16.0671261,14.8255618 C16.8372847,14.0839888 17.037939,12.7646067 16.4890904,12.1033708 C16.2028629,11.7573034 15.7425382,11.5873596 15.1169688,11.5873596 L13.0307538,11.5873596 L12.0628917,16.8 L12.0628917,16.8 Z" id="形状" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const ppt = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/PPT</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/PPT" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="ppt" transform="translate(2.000000, 0.000000)">
            <path d="M14.1176471,0 L2.74128887,0 C1.22731689,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.22731689,24 2.74128887,24 L17.2587111,24 C18.7726831,24 20,22.765548 20,21.2427744 L20,6 C20,6 14.1176471,0 14.1176471,0 Z" id="路径" fill="#F59203"></path>
            <path d="M14.1176471,0 L20,6 L15.3176471,6 C14.6549054,6 14.1176471,5.4627417 14.1176471,4.8 L14.1176471,0 L14.1176471,0 Z" id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0.400000006"></path>
            <path d="M8.54689984,18 L8.54689984,14.7764706 L10.7980922,14.7764706 C13.0111288,14.7764706 14.1176471,13.9058824 14.1176471,12.1764706 C14.1176471,10.4588235 13.0111288,9.6 10.8235294,9.6 L7.05882353,9.6 L7.05882353,18 L8.54689984,18 Z M10.709062,13.6 L8.54689984,13.6 L8.54689984,10.7764706 L10.709062,10.7764706 C11.3577107,10.7764706 11.8410175,10.8823529 12.1462639,11.1058824 C12.4515103,11.3176471 12.6168521,11.6705882 12.6168521,12.1764706 C12.6168521,12.6823529 12.4642289,13.0470588 12.1589825,13.2705882 C11.8537361,13.4823529 11.3704293,13.6 10.709062,13.6 Z" id="P" fill="#FFFFFF" fill-rule="nonzero"></path>
        </g>
    </g>
</svg>`;

export const rar = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/RAR</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/RAR" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="rar" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M10.9766879,14.4620385 L10.9766879,15.9938365 L9.42325733,15.9938365 L9.42325733,14.4620385 L10.9766879,14.4620385 Z M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z M11.7533757,16.759749 L8.64656949,16.759749 L8.64656949,12.164355 L11.7533757,12.164355 L11.7533757,16.759749 Z M11.7534031,5.27127736 L10.2,5.27127736 L10.2,6.80307537 L11.7534031,6.80307537 L11.7534031,8.33487339 L10.2,8.33487339 L10.2,9.8666714 L11.7534031,9.8666714 L11.7534031,11.3984694 L10.1999726,11.3984694 L10.1999726,9.8666714 L8.64656949,9.8666714 L8.64656949,8.33487339 L10.1999726,8.33487339 L10.1999726,6.80307537 L8.64656949,6.80307537 L8.64656949,5.27127736 L10.1999726,5.27127736 L10.1999726,3.73947935 L8.64656949,3.73947935 L8.64656949,2.20768134 L10.2,2.20768134 L10.2,3.73947935 L11.7534031,3.73947935 L11.7534031,5.27127736 Z" id="形状" fill="#667580"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const snippet = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/CODE</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/CODE" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="code" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#53ADEF"></path>
            <path d="M4.8,14.1768 L7.8126,16.1139 L7.8126,14.7156 L6.0189,13.608 L7.8126,12.5004 L7.8126,11.0925 L4.8,13.0395 L4.8,14.1768 Z M9.0498,16.8 L10.0866,16.8 L11.6058,9.6 L10.5621,9.6 L9.0498,16.8 Z M12.5877,11.0925 L12.5877,12.5004 L14.3808,13.608 L12.5877,14.7153 L12.5877,16.1136 L15.6,14.1768 L15.6,13.0395 L12.5877,11.0925 Z" id="形状" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const swf = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/SWF</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/SWF" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="swf" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#DD2A42"></path>
            <path d="M11.313582,13.2692357 L12.922225,13.2692357 L12.922225,14.994972 L10.6066688,14.994972 C9.16864306,18.5966934 6.78000001,18.6216932 6.12221874,18.5966934 L6,18.5966934 L6,16.8709571 C6.95061736,16.8709571 7.94987778,16.7459581 9.04666876,13.7944816 C10.6066688,9.09226891 13.8,9.61751474 13.8,9.61751474 L13.8,11.343251 C11.9962958,11.2682516 11.3140708,13.2692357 11.3140708,13.2692357 L11.313582,13.2692357 Z" id="路径" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const ttf = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/TTF</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/TTF" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="ttf" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#9466CD"></path>
            <path d="M7.3704,16.2138 L7.3704,15.9546 C7.986,15.9546 8.2992,15.6846 8.2992,15.1554 L8.2992,11.1702 C8.2992,10.9542 8.1588,10.8462 7.878,10.8462 C7.5,10.8462 7.1868,10.965 6.9492,11.2134 C6.7116,11.4618 6.528,11.8398 6.42,12.3258 L6.15,12.261 L6.3876,10.425 C6.5172,10.4466 6.6468,10.4574 6.7656,10.4682 C6.8844,10.479 6.9924,10.4898 7.1112,10.4898 L10.4376,10.4898 C10.5564,10.4898 10.686,10.479 10.8048,10.4682 C10.9344,10.4574 11.064,10.4466 11.1936,10.425 L11.1936,12.2286 L10.956,12.261 C10.8912,11.343 10.4268,10.8786 9.5844,10.8786 C9.2928,10.8786 9.1524,10.9758 9.1524,11.1702 L9.1524,15.1554 C9.1524,15.6846 9.4656,15.9546 10.0812,15.9546 L10.0812,16.2138 L7.3704,16.2138 Z" id="路径" fill="#FFFFFF" opacity="0.5"></path>
            <path d="M10.7724,17.7798 L10.7724,17.5206 C11.388,17.5206 11.7012,17.2506 11.7012,16.7214 L11.7012,12.747 C11.7012,12.531 11.5608,12.423 11.28,12.423 C10.902,12.423 10.5888,12.5418 10.3512,12.7902 C10.1136,13.0386 9.9408,13.4058 9.822,13.9026 L9.552,13.8378 L9.7896,12.0018 C9.9192,12.0234 10.0488,12.0342 10.1676,12.045 C10.2864,12.0558 10.3944,12.0666 10.5132,12.0666 L13.8396,12.0666 C13.9584,12.0666 14.088,12.0558 14.2068,12.045 C14.3364,12.0342 14.466,12.0234 14.5956,12.0018 L14.5956,13.8054 L14.358,13.8378 C14.2932,12.9198 13.8288,12.4554 12.9864,12.4554 C12.6948,12.4554 12.5544,12.5526 12.5544,12.747 L12.5544,16.7322 C12.5544,17.2614 12.8676,17.5314 13.4832,17.5314 L13.4832,17.7906 L10.7724,17.7906 L10.7724,17.7798 Z" id="路径" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const txt = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/TXT</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/TXT" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="txt" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#378FE8"></path>
            <path d="M6.06390533,16.8 L6.06390533,12.1033613 L7.75384615,12.1033613 L7.75384615,11.4 L3.6,11.4 L3.6,12.1033613 L5.29704142,12.1033613 L5.29704142,16.8 L6.06390533,16.8 Z M8.84733728,16.8 L10.2035503,14.6596639 L11.5597633,16.8 L12.504142,16.8 L10.6650888,14.0092437 L12.383432,11.4 L11.4390533,11.4 L10.2035503,13.3512605 L8.96804734,11.4 L8.02366864,11.4 L9.72781065,14.0092437 L7.90295858,16.8 L8.84733728,16.8 Z M15.1100592,16.8 L15.1100592,12.1033613 L16.8,12.1033613 L16.8,11.4 L12.6461538,11.4 L12.6461538,12.1033613 L14.3431953,12.1033613 L14.3431953,16.8 L15.1100592,16.8 Z" id="TXT" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const video = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/MP4</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/MP4" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="MP4" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#F59203"></path>
            <path d="M12.6365884,15.4065782 L12.6365884,13.4525858 C12.634444,13.341072 12.6681817,13.2319401 12.7325839,13.1420676 L14.0617532,11.7939643 C14.4974254,11.3546946 14.999556,11.2259432 14.999556,11.7030809 L14.999556,17.1560831 C14.999556,17.6332208 14.4974254,17.5044694 14.0617532,17.0651998 L12.7325839,15.7170964 C12.6681817,15.6272239 12.634444,15.518092 12.6365884,15.4065782 Z M12.0458465,16.7016663 C12.0458465,17.1199454 11.7152413,17.4590277 11.3074191,17.4590277 L6.13842738,17.4590277 C5.7306052,17.4590277 5.4,17.1199454 5.4,16.7016663 L5.4,12.1574977 C5.4,11.7392186 5.7306052,11.4001363 6.13842738,11.4001363 L11.3074191,11.4001363 C11.7152413,11.4001363 12.0458465,11.7392186 12.0458465,12.1574977 L12.0458465,16.7016663 Z" id="形状" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const vss = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/VSS</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/VSS" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="vss" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#FECD4D"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
            <path d="M6.27866109,16.6981132 L8.0460251,11.5018868 L7.23138075,11.5018868 L5.83682008,15.8102426 L5.81610879,15.8102426 L4.41464435,11.5018868 L3.6,11.5018868 L5.36736402,16.6981132 L6.27866109,16.6981132 Z M10.3656904,16.8 C10.9870293,16.8 11.4702929,16.6690027 11.8154812,16.4070081 C12.1606695,16.1377358 12.3332636,15.7738544 12.3332636,15.3080863 C12.3332636,14.8277628 12.1192469,14.4566038 11.6981172,14.1873315 C11.5048117,14.0636119 11.0698745,13.8962264 10.407113,13.6778976 C9.94456067,13.532345 9.66150628,13.4231806 9.55104603,13.3576819 C9.30251046,13.219407 9.18514644,13.0374663 9.18514644,12.8045822 C9.18514644,12.5425876 9.28870293,12.3533693 9.50271967,12.2369272 C9.67531381,12.1350404 9.92384937,12.084097 10.2483264,12.084097 C10.6211297,12.084097 10.8972803,12.1568733 11.0905858,12.3097035 C11.2838912,12.4552561 11.408159,12.7026954 11.4771967,13.0374663 L12.2228033,13.0374663 C12.174477,12.4698113 11.9811715,12.0549865 11.6359833,11.7857143 C11.3115063,11.5237197 10.8627615,11.4 10.289749,11.4 C9.76506276,11.4 9.33012552,11.5237197 8.98493724,11.771159 C8.61903766,12.0331536 8.43953975,12.3897574 8.43953975,12.848248 C8.43953975,13.2994609 8.62594142,13.6487871 9.00564854,13.8889488 C9.15753138,13.9762803 9.53033473,14.1145553 10.1378661,14.3183288 C10.6832636,14.4929919 11.0077406,14.609434 11.1182008,14.667655 C11.4288703,14.8350404 11.5876569,15.0606469 11.5876569,15.351752 C11.5876569,15.5846361 11.4771967,15.7665768 11.2562762,15.8975741 C11.0353556,16.0285714 10.7384937,16.1013477 10.3656904,16.1013477 C9.95146444,16.1013477 9.64769874,16.0212938 9.44748954,15.861186 C9.22656904,15.6865229 9.08849372,15.3881402 9.0332636,14.980593 L8.2876569,14.980593 C8.3290795,15.6355795 8.55,16.115903 8.95732218,16.4215633 C9.29560669,16.6690027 9.76506276,16.8 10.3656904,16.8 Z M14.8324268,16.8 C15.4537657,16.8 15.9370293,16.6690027 16.2822176,16.4070081 C16.6274059,16.1377358 16.8,15.7738544 16.8,15.3080863 C16.8,14.8277628 16.5859833,14.4566038 16.1648536,14.1873315 C15.9715481,14.0636119 15.5366109,13.8962264 14.8738494,13.6778976 C14.4112971,13.532345 14.1282427,13.4231806 14.0177824,13.3576819 C13.7692469,13.219407 13.6518828,13.0374663 13.6518828,12.8045822 C13.6518828,12.5425876 13.7554393,12.3533693 13.9694561,12.2369272 C14.1420502,12.1350404 14.3905858,12.084097 14.7150628,12.084097 C15.0878661,12.084097 15.3640167,12.1568733 15.5573222,12.3097035 C15.7506276,12.4552561 15.8748954,12.7026954 15.9439331,13.0374663 L16.6895397,13.0374663 C16.6412134,12.4698113 16.4479079,12.0549865 16.1027197,11.7857143 C15.7782427,11.5237197 15.3294979,11.4 14.7564854,11.4 C14.2317992,11.4 13.7968619,11.5237197 13.4516736,11.771159 C13.0857741,12.0331536 12.9062762,12.3897574 12.9062762,12.848248 C12.9062762,13.2994609 13.0926778,13.6487871 13.4723849,13.8889488 C13.6242678,13.9762803 13.9970711,14.1145553 14.6046025,14.3183288 C15.15,14.4929919 15.474477,14.609434 15.5849372,14.667655 C15.8956067,14.8350404 16.0543933,15.0606469 16.0543933,15.351752 C16.0543933,15.5846361 15.9439331,15.7665768 15.7230126,15.8975741 C15.5020921,16.0285714 15.2052301,16.1013477 14.8324268,16.1013477 C14.4182008,16.1013477 14.1144351,16.0212938 13.9142259,15.861186 C13.6933054,15.6865229 13.5552301,15.3881402 13.5,14.980593 L12.7543933,14.980593 C12.7958159,15.6355795 13.0167364,16.115903 13.4240586,16.4215633 C13.7623431,16.6690027 14.2317992,16.8 14.8324268,16.8 Z" id="VSS" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>`;

export const xls = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/XLS</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/XLS" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="excel" transform="translate(1.800000, 0.000000)">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#14BC64"></path>
            <polygon id="x" stroke="#10B981" stroke-width="0.5" fill="#FFFFFF" fill-rule="nonzero" points="8.01923077 18 10.2161538 14.7992263 12.3969231 18 14.4 18 11.1530769 13.5319149 14.0446154 9.6 12.0415385 9.6 10.2161538 12.2646035 8.37461538 9.6 6.37153846 9.6 9.26307692 13.5319149 6 18"></polygon>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" fill-rule="nonzero" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const xsd = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/XSD</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/XSD" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="xsd" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z" id="路径" fill="#53ADEF"></path>
            <path d="M4.48846154,16.6981132 L5.76437247,14.6385445 L7.0402834,16.6981132 L7.92874494,16.6981132 L6.198583,14.0126685 L7.81518219,11.5018868 L6.92672065,11.5018868 L5.76437247,13.3795148 L4.60202429,11.5018868 L3.71356275,11.5018868 L5.31680162,14.0126685 L3.6,16.6981132 L4.48846154,16.6981132 Z M10.1732794,16.8 C10.7744939,16.8 11.2421053,16.6690027 11.5761134,16.4070081 C11.9101215,16.1377358 12.0771255,15.7738544 12.0771255,15.3080863 C12.0771255,14.8277628 11.8700405,14.4566038 11.4625506,14.1873315 C11.2755061,14.0636119 10.8546559,13.8962264 10.2133603,13.6778976 C9.76578947,13.532345 9.49190283,13.4231806 9.38502024,13.3576819 C9.14453441,13.219407 9.03097166,13.0374663 9.03097166,12.8045822 C9.03097166,12.5425876 9.13117409,12.3533693 9.33825911,12.2369272 C9.50526316,12.1350404 9.74574899,12.084097 10.0597166,12.084097 C10.4204453,12.084097 10.6876518,12.1568733 10.8746964,12.3097035 C11.0617409,12.4552561 11.1819838,12.7026954 11.2487854,13.0374663 L11.9702429,13.0374663 C11.9234818,12.4698113 11.7364372,12.0549865 11.4024291,11.7857143 C11.0884615,11.5237197 10.654251,11.4 10.0997976,11.4 C9.59210526,11.4 9.17125506,11.5237197 8.83724696,11.771159 C8.48319838,12.0331536 8.30951417,12.3897574 8.30951417,12.848248 C8.30951417,13.2994609 8.48987854,13.6487871 8.85728745,13.8889488 C9.00425101,13.9762803 9.36497976,14.1145553 9.95283401,14.3183288 C10.4805668,14.4929919 10.7945344,14.609434 10.901417,14.667655 C11.2020243,14.8350404 11.355668,15.0606469 11.355668,15.351752 C11.355668,15.5846361 11.2487854,15.7665768 11.0350202,15.8975741 C10.8212551,16.0285714 10.5340081,16.1013477 10.1732794,16.1013477 C9.77246964,16.1013477 9.47854251,16.0212938 9.28481781,15.861186 C9.07105263,15.6865229 8.93744939,15.3881402 8.8840081,14.980593 L8.16255061,14.980593 C8.20263158,15.6355795 8.41639676,16.115903 8.81052632,16.4215633 C9.13785425,16.6690027 9.59210526,16.8 10.1732794,16.8 Z M14.4753036,16.6981132 C15.2435223,16.6981132 15.8246964,16.4652291 16.2255061,15.9994609 C16.6062753,15.548248 16.8,14.9150943 16.8,14.1 C16.8,13.277628 16.6129555,12.6444744 16.2388664,12.2078167 C15.8380567,11.7347709 15.2568826,11.5018868 14.488664,11.5018868 L12.7518219,11.5018868 L12.7518219,16.6981132 L14.4753036,16.6981132 Z M14.3417004,16.0212938 L13.4799595,16.0212938 L13.4799595,12.1787062 L14.3550607,12.1787062 C14.9562753,12.1787062 15.397166,12.3315364 15.6777328,12.6444744 C15.9449393,12.9501348 16.0852227,13.4304582 16.0852227,14.1 C16.0852227,14.7549865 15.9449393,15.23531 15.6710526,15.548248 C15.3904858,15.861186 14.942915,16.0212938 14.3417004,16.0212938 Z" id="XSD" fill="#FFFFFF"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;

export const zip = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.Base基础/1.icon图标/12.文件类型/ZIP</title>
    <g id="1.Base基础/1.icon图标/12.文件类型/ZIP" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="rar" transform="translate(1.800000, 0.000000)" fill-rule="nonzero">
            <path d="M10.9766879,14.4620385 L10.9766879,15.9938365 L9.42325733,15.9938365 L9.42325733,14.4620385 L10.9766879,14.4620385 Z M14.4,0 L2.79611465,0 C1.25186323,0 0,1.23445202 0,2.75725265 L0,21.2427743 C0,22.765548 1.25186323,24 2.79611465,24 L17.6038853,24 C19.1481368,24 20.4,22.765548 20.4,21.2427744 L20.4,6 C20.4,6 14.4,0 14.4,0 Z M11.7533757,16.759749 L8.64656949,16.759749 L8.64656949,12.164355 L11.7533757,12.164355 L11.7533757,16.759749 Z M11.7534031,5.27127736 L10.2,5.27127736 L10.2,6.80307537 L11.7534031,6.80307537 L11.7534031,8.33487339 L10.2,8.33487339 L10.2,9.8666714 L11.7534031,9.8666714 L11.7534031,11.3984694 L10.1999726,11.3984694 L10.1999726,9.8666714 L8.64656949,9.8666714 L8.64656949,8.33487339 L10.1999726,8.33487339 L10.1999726,6.80307537 L8.64656949,6.80307537 L8.64656949,5.27127736 L10.1999726,5.27127736 L10.1999726,3.73947935 L8.64656949,3.73947935 L8.64656949,2.20768134 L10.2,2.20768134 L10.2,3.73947935 L11.7534031,3.73947935 L11.7534031,5.27127736 Z" id="形状" fill="#667580"></path>
            <path d="M14.4,0 L20.4,6 L15.6,6 C14.9372583,6 14.4,5.4627417 14.4,4.8 L14.4,0 L14.4,0 Z" id="矩形" fill="#FFFFFF" opacity="0.400000006"></path>
        </g>
    </g>
</svg>`;
