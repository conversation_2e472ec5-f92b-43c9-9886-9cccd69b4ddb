import { Colors } from '../constants';

export const DEFAULT_FONT_SIZE = 14;
export const DEFAULT_FONT_FAMILY =
    '-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,PingFang SC,Helvetica Neue,Noto Sans,Noto Sans CJK SC,Microsoft Yahei,Arial,Hiragino Sans GB,sans-serif';
export const DEFAULT_FONT_STYLE = 'normal';
export const DEFAULT_TEXT_LINE_HEIGHT = 24;
export const DEFAULT_TEXT_FILL = Colors.gray800;
export const DEFAULT_FONT_WEIGHT = 'normal';
export const DEFAULT_TEXT_WRAP = 'none';
export const DEFAULT_TEXT_TRANSFORMS_ENABLED = 'position';
export const DEFAULT_TEXT_ELLIPSIS = true;
export const DEFAULT_TEXT_LISTENING = false;
export const DEFAULT_TEXT_ALIGN_LEFT = 'left';
export const DEFAULT_TEXT_ALIGN_CENTER = 'center';
export const DEFAULT_TEXT_ALIGN_RIGHT = 'right';
export const DEFAULT_TEXT_VERTICAL_ALIGN_MIDDLE = 'middle';
export const DEFAULT_TEXT_MAX_HEIGHT = 130;
export const DEFAULT_WRAP_TEXT_MAX_ROW = 1;
export const DEFAULT_TEXT_VERTICAL_ALIGN_TOP = 'top';
export const DEFAULT_TEXT_DECORATION = 'none';
export const DEFAULT_TEXT_SCALE = 1;
export const DEFAULT_TEXT_MAX_CACHE = 500;
export const FONT_SIZE_SM = 12;
