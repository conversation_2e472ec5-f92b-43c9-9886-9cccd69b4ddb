export const DEFAULT_ICON_SHAPE = 'square';
export const DEFAULT_ICON_SIZE = 16;

export const Check = `
    M15,1H3C1.9,1,1,1.9,1,3v12c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V3C17,1.9,16.1,1,15,1z M14,6.6l-5.7,5.7
  c-0.2,0.2-0.4,0.3-0.6,0.3s-0.4-0.1-0.6-0.3L4.6,9.8c-0.3-0.3-0.3-0.9,0-1.2s0.9-0.3,1.2,0l1.9,1.9l5.1-5.1
  c0.3-0.3,0.9-0.3,1.2,0C14.3,5.7,14.3,6.3,14,6.6z
  `;

export const Unchecked = `
    M15,1H3C1.9,1,1,1.9,1,3v12c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V3C17,1.9,16.1,1,15,1z
  M15.7,3.3v11.4c0,0.5-0.5,1-1,1H3.3c-0.5,0-1-0.5-1-1V3.3c0-0.5,0.5-1,1-1h11.4C15.2,2.3,15.7,2.8,15.7,3.3z
  `;

export const ColumnTextFilledPath = `M7.268 8.674l-2.076-4.86-2.076 4.86h4.152zm0.513 1.2H2.603l-1.294 3.03L0 12.902L4.49 2.4H5.894l4.484 10.486-1.31 0.002-1.287-3.014zm2.86 1.111c0-1.423 1.246-2.193 4.21-2.49 0-0.89-0.235-1.72-1.362-1.72-0.772 0-1.484 0.356-2.017 0.711l-0.475-0.771c0.653-0.415 1.602-0.889 2.669-0.889 1.602 0 2.313 1.127 2.313 2.787v4.151h-0.948l-0.12-0.83h-0.06c-0.651 0.534-1.422 0.949-2.252 0.949-1.127 0-1.957-0.652-1.957-1.898zm4.113-1.778c-2.253 0.296-3.024 0.83-3.024 1.72 0 0.77 0.534 1.067 1.186 1.067 0.652 0 1.186-0.297 1.838-0.89V9.207z`;

export const ColumnRichTextFilledPath = `M14.5526,11.7084 C14.8845231,11.7084 15.1536,11.9774769 15.1536,12.3094 C15.1536,12.6413231 14.8845231,12.9104 14.5526,12.9104 L14.5526,12.9104 L11.7546,12.9104 C11.4226769,12.9104 11.1536,12.6413231 11.1536,12.3094 C11.1536,11.9774769 11.4226769,11.7084 11.7546,11.7084 L11.7546,11.7084 Z M5.894,2.4 L10.378,12.886 L9.068,12.888 L7.779,9.873 L2.603,9.873 L1.309,12.904 L2.84217094e-14,12.902 L4.49,2.4 L5.894,2.4 Z M5.191,3.814 L3.116,8.673 L7.267,8.673 L5.191,3.814 Z M14.5526,7.17723172 C14.8845231,7.17723172 15.1536,7.44630859 15.1536,7.77823172 C15.1536,8.11015486 14.8845231,8.37923172 14.5526,8.37923172 L14.5526,8.37923172 L10.7546,8.37923172 C10.4226769,8.37923172 10.1536,8.11015486 10.1536,7.77823172 C10.1536,7.44630859 10.4226769,7.17723172 10.7546,7.17723172 L10.7546,7.17723172 Z M14.5526,2.3094 C14.8845231,2.3094 15.1536,2.57847687 15.1536,2.9104 C15.1536,3.24232313 14.8845231,3.5114 14.5526,3.5114 L14.5526,3.5114 L9.7546,3.5114 C9.42267687,3.5114 9.1536,3.24232313 9.1536,2.9104 C9.1536,2.57847687 9.42267687,2.3094 9.7546,2.3094 L9.7546,2.3094 Z`;

export const ColumnSelectFilledPath = `M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zm0-1.2A6.8 6.8 0 1 0 8 1.2a6.8 6.8 0 0 0 0 13.6zM5.666 7.207l1.8 1.845 3.482-3.554.857.84-4.34 4.43-2.658-2.723.86-.838z`;

export const ColumnMultipleFillPath = `M2 2.551L3.576.976l.848.848L2 4.25.576 2.824l.848-.848L2 2.55zm0 5.425L3.576 6.4l.848.849L2 9.673.576 8.249l.848-.849.576.576zm0 5.4L3.576 11.8l.848.849L2 15.073.576 13.649l.848-.849.576.576zM5.663 2.6a.6.6 0 1 1 0-1.2H15.4a.6.6 0 0 1 0 1.2H5.663zm0 11.8a.6.6 0 0 1 0-1.2H15.4a.6.6 0 0 1 0 1.2H5.663zm0-6.2a.6.6 0 1 1 0-1.2H15.4a.6.6 0 0 1 0 1.2H5.663z`;

export const ColumnCalendarFilledPath = `M10.9 3.2H5.1v1.6H3.9V3.2H1.2v3.2h13.6V3.2h-2.7v1.6h-1.2V3.2zM12.1 2H15a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h2.9V.4h1.2V2h5.8V.4h1.2V2zm2.7 5.6H1.2v7.2h13.6V7.6z`;

export const ColumnNumberFilledPath = `M4.236 9.9l.422-3.8H2.6a.6.6 0 1 1 0-1.2h2.19l.372-3.347a.6.6 0 1 1 1.192.133L5.998 4.9h4.793l.37-3.347a.6.6 0 0 1 1.193.133L11.998 4.9h2.459a.6.6 0 0 1 0 1.2h-2.592l-.421 3.8h2.013a.6.6 0 0 1 0 1.2H11.31l-.374 3.368a.6.6 0 0 1-1.192-.132l.358-3.236H5.311l-.374 3.368a.6.6 0 0 1-1.192-.132l.358-3.236H1.6a.6.6 0 0 1 0-1.2h2.636zm1.208 0h4.792l.422-3.8H5.865l-.421 3.8z`;

export const ColumnLinkOutlinedPath = `M10.4238543,5.91643446 C11.7563563,7.48802206 11.6811634,9.84553933 10.1982756,11.3284271 L7.22842712,14.2982756 C5.66632996,15.8603728 3.13367004,15.8603728 1.57157288,14.2982756 C0.00947570825,12.7361784 0.00947570825,10.2035185 1.57157288,8.64142136 L2.387,7.82495116 L3.236,8.67395116 L2.42010101,9.48994949 C1.37219416,10.5378563 1.32853138,12.2097205 2.28911266,13.3096237 L2.42010101,13.4497475 C3.51356903,14.5432155 5.28643097,14.5432155 6.37989899,13.4497475 L9.34974747,10.479899 C10.3636395,9.46600698 10.4374244,7.86800734 9.57110232,6.76909967 L10.4238543,5.91643446 Z M14.4436815,1.67157288 C16.0057786,3.23367004 16.0057786,5.76632996 14.4436815,7.32842712 L13.5744058,8.198 L12.7254058,7.349 L13.5951533,6.47989899 C14.6430602,5.43199214 14.6867229,3.76012802 13.7261417,2.66022473 L13.5951533,2.52010101 C12.5016853,1.426633 10.7288234,1.426633 9.63535534,2.52010101 L6.66550686,5.48994949 C5.62228095,6.5331754 5.57434734,8.19479666 6.52170603,9.29485 L5.67236452,10.1460046 C4.25646867,8.57570748 4.3046734,6.15372668 5.81697872,4.64142136 L8.7868272,1.67157288 C10.3489244,0.109475708 12.8815843,0.109475708 14.4436815,1.67157288 Z`;

export const ColumnRatingFilledPath = `M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zm0 1.2a6.8 6.8 0 1 0 0 13.6A6.8 6.8 0 0 0 8 1.2zm.945 2.906l.077.106.064.112.03.069.564 1.46 1.646.1c.952.092 1.365 1.264.752 1.893l-.086.078-1.315 1.056.431 1.533c.295 1.031-.848 1.858-1.643 1.34l-.087-.063-1.315-.85-.006.001-1.309.842c-.85.567-1.903-.163-1.79-1.054l.019-.108.45-1.601-1.321-1.035c-.632-.633-.372-1.673.462-1.973l.123-.038.124-.026 1.571-.094.602-1.55c.364-.793 1.44-.836 1.957-.198zm-.91.815l-.809 2.085-2.253.134L6.8 8.571l-.602 2.139 1.44-.927.71-.087 1.569 1.016-.61-2.172 1.75-1.402-2.219-.134-.803-2.083z`;

export const ColumnMemberFilledPath = `M10.212 8.416A6.603 6.603 0 0 1 14.5 14.6a.6.6 0 0 1-1.2 0 5.4 5.4 0 0 0-10.8 0 .6.6 0 0 1-1.2 0 6.602 6.602 0 0 1 4.159-6.134 4.1 4.1 0 1 1 4.753-.05zM7.8 8a2.9 2.9 0 1 0 0-5.8 2.9 2.9 0 0 0 0 5.8z`;

export const ColumnProgressFilledPath = `M11.5 3.5a4.5 4.5 0 1 1 0 9h-7a4.5 4.5 0 1 1 0-9h7zm0 1.2h-7a3.3 3.3 0 0 0 0 6.6h7a3.3 3.3 0 1 0 0-6.6zM8 7.2v1.5H3.75a.75.75 0 0 1 0-1.5H8z`;

export const ColumnCheckboxFilledPath = `M2.2,13.8 L13.8,13.8 L13.8,2.2 L2.2,2.2 L2.2,13.8 Z M13.904,1 C14.509,1 15,1.491 15,2.097 L15,13.903 C15,14.509 14.509,15 13.904,15 L2.096,15 C1.491,15 1,14.509 1,13.903 L1,2.097 C1,1.491 1.491,1 2.096,1 L13.904,1 Z M11.1374,5.4697 C11.3104,5.4697 11.4834,5.5387 11.6094,5.6767 C11.8484,5.9377 11.8304,6.3427 11.5704,6.5807 L7.3134,10.4847 C7.1904,10.5977 7.0354,10.6537 6.8814,10.6537 C6.7084,10.6537 6.5354,10.5827 6.4094,10.4457 C6.4034,10.4397 6.4024,10.4307 6.3964,10.4247 L4.3634,8.2067 C4.1244,7.9477 4.1424,7.5417 4.4024,7.3027 C4.5254,7.1907 4.6804,7.1347 4.8354,7.1347 C5.0084,7.1347 5.1804,7.2047 5.3064,7.3427 L6.9234,9.1057 L10.7054,5.6377 C10.8274,5.5257 10.9834,5.4697 11.1374,5.4697 Z`;

export const AddOutlinedPath = `M8.578 7.383V1.602a.601.601 0 1 0-1.2 0v5.781H1.6a.601.601 0 0 0 0 1.203h5.777v5.812a.601.601 0 1 0 1.2 0V8.586H14.4a.601.601 0 0 0 0-1.203H8.578z`;

export const MoreStandOutlinedPath = `M8 4.25a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5zm0 5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5zm0 5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5z`;

export const StarFill = `M9.186 2.12l1.02 2.769c.073.292.365.437.656.437l2.915.146c1.166.073 1.676 1.53.73 2.26l-2.26 1.821c-.219.146-.291.51-.219.729l.73 2.842c.29 1.093-.948 2.04-1.896 1.384l-2.477-1.603a.79.79 0 0 0-.802 0l-2.478 1.676c-.947.583-2.186-.291-1.894-1.457l.728-2.842c.073-.292 0-.583-.218-.729L1.46 7.731c-.874-.729-.437-2.186.73-2.259l2.914-.146c.292 0 .51-.218.656-.437l1.093-2.77c.365-1.092 1.895-1.092 2.332 0z`;

export const WebOutlinedPath = `M1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8ZM6.04798 2.85646C4.19852 3.55874 2.82616 5.22986 2.55071 7.25H5.26373C5.31861 5.76009 5.53564 4.40148 5.87147 3.352C5.92605 3.18143 5.98481 3.0157 6.04798 2.85646ZM5.87147 12.648C5.53564 11.5985 5.31861 10.2399 5.26373 8.75H2.55071C2.82616 10.7701 4.19852 12.4413 6.04798 13.1435C5.98481 12.9843 5.92605 12.8186 5.87147 12.648ZM10.1285 12.648C10.0739 12.8186 10.0152 12.9843 9.95202 13.1435C11.8015 12.4413 13.1738 10.7701 13.4493 8.75H10.7363C10.6814 10.2399 10.4644 11.5985 10.1285 12.648ZM8.6999 12.1908C8.98163 11.3104 9.18095 10.1118 9.23518 8.75H6.76482C6.81905 10.1118 7.01837 11.3104 7.3001 12.1908C7.46768 12.7145 7.65131 13.0839 7.81962 13.3063C7.90746 13.4224 7.9686 13.4711 8 13.4906C8.0314 13.4711 8.09254 13.4224 8.18038 13.3063C8.34869 13.0839 8.53232 12.7145 8.6999 12.1908ZM7.3001 3.80916C7.01837 4.68959 6.81905 5.88818 6.76482 7.25H9.23518C9.18095 5.88818 8.98163 4.68959 8.6999 3.80916C8.53232 3.28548 8.34869 2.91612 8.18038 2.69367C8.09254 2.57756 8.0314 2.52893 8 2.50944C7.9686 2.52893 7.90746 2.57756 7.81962 2.69367C7.65131 2.91612 7.46768 3.28548 7.3001 3.80916ZM10.1285 3.352C10.4644 4.40148 10.6814 5.76009 10.7363 7.25H13.4493C13.1738 5.22986 11.8015 3.55874 9.95203 2.85646C10.0152 3.0157 10.0739 3.18143 10.1285 3.352Z`;

export const DepartmentOutlinedPath = `M3 5.5C3 3.567 4.567 2 6.5 2C8.433 2 10 3.567 10 5.5C10 6.49548 9.5844 7.3939 8.91724 8.0312C10.7204 8.86779 12.017 10.6115 12.2217 12.6767C12.2976 13.4424 11.6682 14 11 14H2C1.33185 14 0.702358 13.4424 0.778277 12.6767C0.983033 10.6115 2.27961 8.86779 4.08276 8.0312C3.4156 7.3939 3 6.49548 3 5.5ZM8.5 5.5C8.5 4.39543 7.60457 3.5 6.5 3.5C5.39543 3.5 4.5 4.39543 4.5 5.5C4.5 6.60457 5.39543 7.5 6.5 7.5C7.60457 7.5 8.5 6.60457 8.5 5.5ZM2.31585 12.5H10.6841C10.3303 10.5108 8.59114 9 6.5 9C4.40886 9 2.66975 10.5108 2.31585 12.5Z', 'M11 3C10.5858 3 10.25 3.33579 10.25 3.75C10.25 4.16421 10.5858 4.5 11 4.5C11.6904 4.5 12.25 5.05964 12.25 5.75C12.25 6.44036 11.6904 7 11 7C10.5858 7 10.25 7.33579 10.25 7.75C10.25 8.16421 10.5858 8.5 11 8.5C12.6569 8.5 14 9.84315 14 11.5C14 11.9142 14.3358 12.25 14.75 12.25C15.1642 12.25 15.5 11.9142 15.5 11.5C15.5 9.77473 14.5291 8.27622 13.1038 7.52106C13.507 7.0426 13.75 6.42467 13.75 5.75C13.75 4.23122 12.5188 3 11 3Z`;

export const AttachmentPath = `M9.1773124,11.8057395 C7.96668709,13.0163648 6.00387566,13.0163648 4.79325035,11.8057395 C3.58262505,10.5951142 3.58262505,8.63230278 4.78926443,7.4257012 L8.27628904,3.87217601 C8.50840068,3.63567937 8.88828274,3.63209333 9.12479868,3.86418566 C9.36131462,4.096278 9.36490066,4.47616006 9.13280833,4.712676 L5.64177849,8.27020561 C4.89978234,9.01220177 4.89978234,10.2152152 5.64177849,10.9572114 C6.38377464,11.6992075 7.5867881,11.6992075 8.32878426,10.9572114 L12.2321177,7.05387799 C13.3493901,5.93660552 13.2780395,4.02707269 12.0410949,2.79012806 C10.8041502,1.55318343 8.8946174,1.4818328 7.77734493,2.59910526 L3.72686067,6.64958953 C2.02034367,8.35610653 2.02835883,11.1329242 3.74721224,12.8517776 C5.46606565,14.570631 8.24288334,14.5786462 9.94940034,12.8721292 L13.5980637,9.22346588 C13.8323782,8.98915131 14.2122772,8.98915131 14.4465918,9.22346588 C14.6809064,9.45778046 14.6809064,9.83767945 14.4465918,10.071994 L10.7979285,13.7206573 C8.62168228,15.8969035 5.08507361,15.8866953 2.8986841,13.7003058 C0.712294592,11.5139163 0.702086332,7.97730759 2.87833253,5.8010614 L6.9288168,1.75057713 C8.52947856,0.149915361 11.1976354,0.249612361 12.889623,1.94159992 C14.5816106,3.63358749 14.6813076,6.30174436 13.0806458,7.90240612 L9.1773124,11.8057395 Z`;

export const EditPath = `M2,8.33918294 L10.7095952,0 L15,4.17020474 L6.53816398,13 L2,13 L2,8.33918294 Z M3.2,8.85157902 L3.2,11.8 L6.02608482,11.8 L13.3091776,4.20020516 L10.7033283,1.66736141 L3.2,8.85157902 Z M0,16 L0,14.8 L16,14.8 L16,16 L0,16 Z`;

export const RowDragPath = `M6,3 C6.55228475,3 7,2.55228475 7,2 C7,1.44771525 6.55228475,1 6,1 C5.44771525,1 5,1.44771525 5,2 C5,2.55228475 5.44771525,3 6,3 Z M10,3 C10.5522847,3 11,2.55228475 11,2 C11,1.44771525 10.5522847,1 10,1 C9.44771525,1 9,1.44771525 9,2 C9,2.55228475 9.44771525,3 10,3 Z M6,7 C6.55228475,7 7,6.55228475 7,6 C7,5.44771525 6.55228475,5 6,5 C5.44771525,5 5,5.44771525 5,6 C5,6.55228475 5.44771525,7 6,7 Z M10,7 C10.5522847,7 11,6.55228475 11,6 C11,5.44771525 10.5522847,5 10,5 C9.44771525,5 9,5.44771525 9,6 C9,6.55228475 9.44771525,7 10,7 Z M6,11 C6.55228475,11 7,10.5522847 7,10 C7,9.44771525 6.55228475,9 6,9 C5.44771525,9 5,9.44771525 5,10 C5,10.5522847 5.44771525,11 6,11 Z M10,11 C10.5522847,11 11,10.5522847 11,10 C11,9.44771525 10.5522847,9 10,9 C9.44771525,9 9,9.44771525 9,10 C9,10.5522847 9.44771525,11 10,11 Z M6,15 C6.55228475,15 7,14.5522847 7,14 C7,13.4477153 6.55228475,13 6,13 C5.44771525,13 5,13.4477153 5,14 C5,14.5522847 5.44771525,15 6,15 Z M10,15 C10.5522847,15 11,14.5522847 11,14 C11,13.4477153 10.5522847,13 10,13 C9.44771525,13 9,13.4477153 9,14 C9,14.5522847 9.44771525,15 10,15 Z`;

export const AngleDownPath = `M7.97815,11.99732 L7.9728,12.00267 L2.2998,6.33067 L3.1298,5.49967 L7.97807731,10.3479473 L12.8255,5.49967 L13.6565,6.33067 L7.9835,12.00267 L7.97815,11.99732 Z`;

export const AngleRightPath = `M4.72665,3.40415 L5.55765,2.57315 L11.22965,8.24615 L5.55765,13.92985 L4.72665,13.09985 L9.57465,8.25285`;

export const IconPathMap: Record<string, string> = {};
