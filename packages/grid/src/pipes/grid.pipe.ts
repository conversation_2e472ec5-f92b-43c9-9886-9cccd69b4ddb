import { Pipe, PipeTransform } from '@angular/core';
import {
    AITableReferences,
    AITableField,
    AITableFieldOption,
    AITableFieldSettings,
    AITableSelectOption,
    MemberSettings,
    SelectSettings
} from '@ai-table/utils';
import { isSameFieldOption } from '../core';
import { AITableSelection } from '../types';

@Pipe({
    name: 'selectOption'
})
export class SelectOptionPipe implements PipeTransform {
    transform(_id: string, options: AITableSelectOption[]) {
        return options?.length && options.find((item) => item._id === _id);
    }
}

@Pipe({
    name: 'selectOptions'
})
export class SelectOptionsPipe implements PipeTransform {
    transform(ids: string[], options: AITableSelectOption[] = []) {
        return (
            (ids?.length &&
                ids.map((id: string) => {
                    return options.find((item) => item._id === id);
                })) ||
            []
        );
    }
}

@Pipe({
    name: 'isSelectRecord'
})
export class IsSelectRecordPipe implements PipeTransform {
    transform(recordId: string, selection: AITableSelection) {
        return selection.selectedRecords.has(recordId);
    }
}

@Pipe({
    name: 'user'
})
export class UserPipe implements PipeTransform {
    transform(values: string[], references: AITableReferences) {
        return (
            (values.length &&
                values.map((item) => {
                    return references.members[item] || {};
                })) ||
            []
        );
    }
}

@Pipe({
    name: 'selectSetting'
})
export class SelectSettingPipe implements PipeTransform {
    transform(settings: AITableFieldSettings) {
        return settings as SelectSettings;
    }
}

@Pipe({
    name: 'memberSetting'
})
export class MemberSettingPipe implements PipeTransform {
    transform(settings: AITableFieldSettings) {
        return settings as MemberSettings;
    }
}

@Pipe({
    name: 'fieldIsSameOption'
})
export class AITableFieldIsSameOptionPipe implements PipeTransform {
    transform(fieldOption: AITableFieldOption, field: Partial<AITableField>) {
        return isSameFieldOption(fieldOption, field);
    }
}
