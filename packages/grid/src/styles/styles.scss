/* You can add global styles to this file, and also import other style files */
@use 'ngx-tethys/styles/index.scss';
@use 'ngx-tethys/styles/variables.scss';
@use '../components/cell-editors/cell-editor.scss';
@use '../components/cell-views/select/option.scss';
@use '../components/field-menu/field-menu.scss';
@use '../components/context-menu/context-menu.scss';
@use '../components/field-setting/field-setting.component.scss';
@use '../components/drag/drag.component.scss';
@use '../components/menu-checkbox-sort/checkbox-menu-sort.component.scss';

.ai-table-dom-grid {
    display: table;
    width: 100%;

    .grid-header {
        border-top: 1px solid variables.$gray-200;

        .grid-field:not(.highlight) {
            &:hover {
                background-color: variables.$gray-80;
            }
        }
    }

    .grid-body {
        flex-direction: column;
    }
    .grid-row,
    .grid-header {
        border-bottom: 1px solid variables.$gray-200;
    }

    .grid-row {
        &:hover {
            background-color: variables.$gray-80;
            .unchecked-box {
                display: block;
            }
            .grid-row-number {
                display: none;
            }
        }
        &.highlight {
            background: variables.$secondary-item-active;
        }
    }

    .grid-cell {
        height: 44px;
        display: flex;
        align-items: center;
        width: 300px;
        border-left: 1px solid variables.$gray-200;
        padding: 0 12px;
        position: relative;
        cursor: pointer;
        justify-content: space-between;
        .autofill-container {
            position: absolute;
            width: 8px;
            height: 8px;
            right: -5px;
            bottom: -5px;
            border: 2px solid variables.$white;
            background: variables.$primary;
            cursor: crosshair;
            z-index: 100;
            display: none;
        }
        &.highlight {
            background: variables.$secondary-item-active;
        }
        &.selected {
            border: 2px solid variables.$primary;
            background: variables.$white;
            .autofill-container {
                display: block;
            }
        }
    }

    .grid-field {
        .grid-field-action {
            visibility: hidden;
        }
        &:hover {
            .grid-field-action {
                visibility: visible;
            }
        }
    }

    .grid-column-blank {
        flex: 1;
        min-width: 180px;
        display: flex;
        padding-left: 14px;
        align-items: center;
        border-left: 1px solid variables.$gray-200;
    }

    .grid-header {
        .grid-column-blank {
            &:hover {
                background-color: variables.$gray-80;
            }
        }
    }
    .grid-row-index {
        .checked-box {
            display: block;
        }
        .unchecked-box {
            display: none;
        }
        .grid-row-no-number {
            display: none;
        }

        .grid-row-number {
            display: block;
        }
    }

    .grid-row-index,
    .grid-column-checkbox {
        width: 44px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: variables.$gray-500;
        border-left: none;
    }

    .grid-row-insert {
        height: 44px;
        display: flex;
        padding-left: 16px;
        align-items: center;
    }

    .cell-active {
        border: 1px solid variables.$primary;
        margin-left: 0.5px;
    }

    .thy-icon-plus {
        font-size: 16px;
        color: variables.$gray-600;
    }
}

.ai-table-grid {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;

    .ai-table-grid-view {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .ai-table-left-background-wrapper {
        position: absolute;
        left: 0;
        width: 18px;
        overflow: hidden;
    }

    .ai-table-left-background {
        position: absolute;
        top: 0px;
        left: 0px;
        cursor: pointer;
        border-radius: 12px 0px 0px 12px;
        background: #ff9f73;
        width: 18px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        transform: translateY(var(--scroll-top, 0));

        thy-icon {
            display: block;
            margin: 0;
            line-height: 1;
            font-size: 14px;
        }
    }

    .ai-table-horizontal-scroll-bar-wrapper,
    .ai-table-vertical-scroll-bar-wrapper {
        position: absolute;
        will-change: transform;
        cursor: pointer;

        .ai-table-scroll-bar-inner {
            position: absolute;
        }

        &::scrollbar {
            width: 18px;
            height: 18px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        &::scrollbar-button {
            display: none;
            height: 0;
            width: 0;
        }
        &::scrollbar-thumb {
            background-color: variables.$gray-500;
            background-clip: padding-box;
            border: calc(18px / 4) solid variables.$black;
            border-radius: calc(18px / 2);
            min-height: 36px;
        }
        &:hover {
            &::scrollbar-thumb {
                background-color: variables.$gray-500;
            }
        }
    }

    .ai-table-vertical-scroll-bar-wrapper {
        overflow-x: hidden;
        overflow-y: scroll;
        width: 18px;
        right: 0;

        .ai-table-scroll-bar-inner {
            width: 1px;
        }
    }

    .ai-table-horizontal-scroll-bar-wrapper {
        overflow-x: scroll;
        overflow-y: hidden;
        height: 18px;
        bottom: 0;
        left: 0;

        .ai-table-scroll-bar-inner {
            height: 1px;
        }
    }
}
