import { AIRecordFieldIdPath, AITableField, AITableRecord, AITableRecordUpdatedInfo, IdPath, NumberPath } from './core';
import { AITableView, AITableViewRecord, Positions, RemovePositions } from './view';

export enum ActionName {
    UpdateFieldValue = 'update_field_value',
    AddRecord = 'add_record',
    AddField = 'add_field',
    RemoveField = 'remove_field',
    RemoveRecord = 'remove_record',
    SetField = 'set_field',
    SetView = 'set_view',
    AddView = 'add_view',
    RemoveView = 'remove_view',
    SetRecordPositions = 'set_record_positions',
    UpdateSystemFieldValue = 'update_system_field_value'
}

export enum ExecuteType {
    Execute,
    Undo,
    Redo
}

export type UpdateFieldValueAction = {
    type: ActionName.UpdateFieldValue;
    path: AIRecordFieldIdPath;
    newFieldValue: any;
};

export type AddRecordAction = {
    type: ActionName.AddRecord;
    record: AITableRecord | AITableViewRecord;
};

export type AddFieldAction = {
    type: ActionName.AddField;
    field: AITableField;
    originId?: string;
    isDuplicate?: boolean;
};

export type RemoveFieldAction = {
    type: ActionName.RemoveField;
    path: IdPath;
};

export type RemoveRecordAction = {
    type: ActionName.RemoveRecord;
    path: IdPath;
};

export type SetFieldAction = {
    type: ActionName.SetField;
    path: IdPath;
    properties: Partial<AITableField>;
    newProperties: Partial<AITableField>;
};

export interface SetViewAction {
    type: ActionName.SetView;
    properties: Partial<AITableView>;
    newProperties: Partial<AITableView>;
    path: IdPath;
}

export interface AddViewAction {
    type: ActionName.AddView;
    view: AITableView;
    isDuplicate?: boolean;
}

export interface RemoveViewAction {
    type: ActionName.RemoveView;
    path: IdPath;
}

export interface SetRecordPositionAction {
    type: ActionName.SetRecordPositions;
    positions: Positions | RemovePositions;
    path: NumberPath;
}

export interface UpdateSystemFieldValue {
    type: ActionName.UpdateSystemFieldValue;
    updatedInfo: Partial<AITableRecordUpdatedInfo>;
    path: IdPath;
}

export type AITableViewAction = SetViewAction | AddViewAction | RemoveViewAction;

export type AITableSystemFieldAction = SetRecordPositionAction | UpdateSystemFieldValue;

export type AITableAction =
    | UpdateFieldValueAction
    | AddRecordAction
    | AddFieldAction
    | RemoveRecordAction
    | RemoveFieldAction
    | SetFieldAction
    | AITableViewAction
    | AITableSystemFieldAction;
