# GroupCalculator 折叠功能 Bug 分析报告

## 问题概述

在 `GroupCalculator` 类中，折叠功能存在逻辑错误。当一级分组被折叠时，其子分组没有被正确隐藏，导致用户界面显示不正确的分组结构。

## 问题详情

### 受影响的方法

- **文件**: `/packages/state/src/utils/group/group-calculator.ts`
- **方法**: `filterVisibleGroupTabs` (第286-310行)

### 问题表现

当分组 `'1'` (ID: `level1_0_0`) 被折叠时：

**期望行为**：

- 一级分组 `'1'` 显示为折叠状态
- 所有属于分组 `'1'` 的子分组都应该被隐藏
- 所有属于分组 `'1'` 的记录都应该被隐藏

**实际行为**：

- ✅ 一级分组 `'1'` 正确显示为折叠状态 (`isCollapsed: true`)
- ✅ 记录被正确隐藏 (shouldShowRecord 方法工作正常)
- ❌ 子分组仍然可见：
    - `1-1-2` (三级分组, ID: level3_2_1)
    - `1-2` (二级分组, ID: level2_1_1)
    - `1-2-1` (三级分组, ID: level3_2_2)

### 根本原因

`filterVisibleGroupTabs` 方法的逻辑缺陷：

```typescript
// 当前有问题的逻辑
for (let parentDepth = 0; parentDepth < currentDepth; parentDepth++) {
    // 找到同一记录索引下的父级分组ID
    const parentRow = groupTabRows.find((row) => row.depth === parentDepth);
    if (parentRow && this.groupCollapseState.has(parentRow.groupId)) {
        show = false;
        break;
    }
}
```

**问题分析**：

1. 只能检查同一批 `groupTabRows` 中的父级分组
2. 无法跨记录查找真正的分组层级关系
3. 当处理不同记录时，无法找到正确的父级分组状态

## 测试验证

通过测试用例 `should collapse first-level group and hide all sub-groups when using collapsed_group_ids` 可以验证问题：

```
被折叠的分组ID: level1_0_0
可见的二级分组数量: 2
  二级分组: 1-2 (ID: level2_1_1)  // ❌ 应该被隐藏
  二级分组: 2-1 (ID: level2_1_2)  // ✅ 正确可见

可见的三级分组数量: 3
  三级分组: 1-1-2 (ID: level3_2_1)  // ❌ 应该被隐藏
  三级分组: 1-2-1 (ID: level3_2_2)  // ❌ 应该被隐藏
  三级分组: 2-1-1 (ID: level3_2_3)  // ✅ 正确可见
```

## 影响范围

1. **用户体验**：折叠功能无法按预期工作
2. **界面显示**：分组结构显示混乱
3. **功能完整性**：核心功能缺陷

## 解决方案建议

### 方案1：重写 filterVisibleGroupTabs 方法 (推荐)

需要建立正确的分组层级关系映射，而不是依赖同一批分组标签的查找。

```typescript
private filterVisibleGroupTabs(groupTabRows: AITableLinearRowGroup[]): AITableLinearRowGroup[] {
    // 建议重新实现，建立正确的分组层级关系
    // 需要根据分组值和字段ID建立父子关系，而不是仅依赖depth
}
```

### 方案2：预处理分组状态

在生成分组标签之前，预先计算所有需要隐藏的分组ID。

### 方案3：后处理过滤

在生成完整结果后，再次过滤掉属于折叠分组的子内容。

## 临时解决方案

如果需要快速修复，可以在 `generateLinearRows` 方法中添加额外的过滤逻辑，检查每个分组是否属于已折叠的父级分组。

## 测试用例状态

- ✅ 基础分组功能测试通过
- ❌ 折叠功能测试失败 (已调整为分析模式)
- 📋 需要在修复后更新测试断言

## 相关文件

- **主要文件**: `/packages/state/src/utils/group/group-calculator.ts`
- **测试文件**: `/packages/state/src/testing/utils/group/group-calculator.spec.ts`
- **构建文件**: `/packages/state/src/utils/build.ts` (第25行调用)

## 优先级

**高优先级** - 这是核心功能的重要缺陷，影响用户体验和功能完整性。

---

_报告生成时间: 2025-09-05_
_状态: 待修复_
