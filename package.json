{"name": "ai-table", "version": "0.1.41", "workspaces": ["packages/*"], "scripts": {"ng": "ng", "start": "ng serve demo", "start-ws": "npx ts-node ./backend/src/index", "build": "npm run build:packages && npm run sync-styles", "build:packages": "ts-node --project ./scripts/tsconfig.json ./scripts/build-packages.ts", "build:demo": "ng build demo --configuration production", "build:ws": "tsc -p tsconfig.backend.json", "sync-styles": "ts-node --project ./scripts/tsconfig.json ./scripts/sync-styles.ts", "release": "npm run build && wpm release --release-branch-format release-auto-v{{version}}", "release-next": "npm run build && wpm release --release-branch-format release-auto-next-v{{version}}", "release-manual": "npm run build && wpm release", "release-next-manual": "npm run build && wpm release --release-branch-format release-next-v{{version}}", "watch": "ng build --watch --configuration development", "pub": "wpm publish && npm run pub-only", "pub-next": "wpm publish && npm run pub-next-only", "pub-only": "ts-node --project ./scripts/tsconfig.json ./scripts/npm-publish.ts", "pub-next-only": "ts-node --project ./scripts/tsconfig.json ./scripts/npm-publish.ts --next", "test": "npm run test:state", "test:grid": "ng test grid", "test:state": "ng test state"}, "repository": {"type": "git", "url": "https://github.com/worktile/ai-table"}, "dependencies": {"@angular/animations": "^19.2.7", "@angular/cdk": "^19.2.7", "@angular/common": "^19.2.7", "@angular/compiler": "^19.2.7", "@angular/core": "^19.2.7", "@angular/forms": "^19.2.7", "@angular/platform-browser": "^19.2.7", "@angular/platform-browser-dynamic": "^19.2.7", "@angular/router": "^19.2.7", "@tethys/cdk": "^19.0.7", "@tethys/icons": "^1.4.77", "bson-objectid": "^2.0.4", "nanoid": "^3.3.8", "date-fns": "^4.1.0", "grapheme-splitter": "^1.0.4", "immer": "^10.0.3", "ngx-tethys": "^19.1.0", "konva": "^9.3.14", "lodash": "^4.17.21", "lru-cache": "^11.0.0", "npm": "^10.8.1", "rxjs": "~7.8.0", "tslib": "^2.6.3", "zone.js": "~0.15.0", "is-hotkey": "^0.2.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.8", "@angular/cli": "^19.2.8", "@angular/compiler-cli": "^19.2.7", "@types/jasmine": "~5.1.4", "@types/lodash": "^4.17.7", "@types/node": "^10.14.9", "@types/ws": "^7.4.7", "@worktile/pkg-manager": "^0.1.0", "chalk": "^2.4.2", "cpx": "^1.5.0", "jasmine-core": "~5.2.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^19.2.2", "prettier": "^3.3.2", "ts-node": "^10.9.2", "typescript": "~5.8.3", "ws": "8.0.0", "y-websocket": "^2.0.3", "yjs": "13.6.26", "@types/is-hotkey": "^0.1.6"}}