/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
    "compileOnSave": false,
    "compilerOptions": {
        "baseUrl": "./",
        "outDir": "./dist/out-tsc",
        "strict": true,
        "noImplicitOverride": true,
        "noPropertyAccessFromIndexSignature": true,
        "noImplicitReturns": true,
        "noFallthroughCasesInSwitch": true,
        "skipLibCheck": true,
        "esModuleInterop": true,
        "sourceMap": true,
        "declaration": false,
        "experimentalDecorators": true,
        "moduleResolution": "bundler",
        "importHelpers": false,
        "target": "ES2022",
        "useDefineForClassFields": false,
        "lib": [
            "ES2022",
            "dom"
        ],
        "paths": {
            "@ai-table/grid": [
                "packages/grid/src/public-api"
            ],
            "@ai-table/grid/*": [
                "packages/grid/src/*"
            ],
            "@ai-table/state": [
                "packages/state/src/public-api"
            ],
            "@ai-table/state/*": [
                "packages/state/src/*"
            ],
            "@ai-table/utils": [
                "packages/utils/src/public-api"
            ],
            "@ai-table/utils/*": [
                "packages/utils/src/*"
            ]
        }
    },
    "angularCompilerOptions": {
        "enableI18nLegacyMessageIdFormat": false,
        "strictInjectionParameters": true,
        "strictInputAccessModifiers": true,
        "strictTemplates": true,
        "strictStandalone": true,
        "compilationMode": "partial"
    }
}