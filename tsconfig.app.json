/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
    "extends": "./tsconfig.json",
    "compilerOptions": {
        "outDir": "./out-tsc/app",
        "types": [],
        "paths": {
            "@ai-table/grid": [
                "./packages/grid/src/index"
            ],
            "@ai-table/state": [
                "./packages/state/src/public-api"
            ],
            "@ai-table/utils": [
                "./packages/utils/src/index"
            ]
        },
        "moduleResolution": "node",
        "importHelpers": true,
        "module": "es2020",
        "lib": [
            "es2022",
            "dom"
        ]
    },
    "files": [
        "src/main.ts"
    ],
    "include": [
        "src/**/*.d.ts"
    ]
}